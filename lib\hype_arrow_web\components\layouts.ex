defmodule HypeArrowWeb.Layouts do
  @moduledoc """
  This module holds different layouts used by your application.

  See the `layouts` directory for all templates available.
  The "root" layout is a skeleton rendered as part of the
  application router. The "app" layout is set as the default
  layout on both `use HypeArrowWeb, :controller` and
  `use HypeArrowWeb, :live_view`.
  """
  use HypeArrowWeb, :html

  embed_templates "layouts/*"
end
