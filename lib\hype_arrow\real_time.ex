defmodule HypeArrow.RealTime do
  @moduledoc """
  Handles real-time updates using Phoenix PubSub.
  
  This module provides functions for broadcasting and subscribing to real-time events
  in the application, replacing the polling mechanism with a more efficient pub/sub approach.
  """
  
  alias Phoenix.PubSub
  
  @pubsub HypeArrow.PubSub
  
  @doc """
  Subscribe to updates for a specific topic.
  
  ## Examples
  
      iex> subscribe(:posts)
      :ok
  """
  def subscribe(topic) when is_atom(topic) do
    PubSub.subscribe(@pubsub, topic_name(topic))
  end
  
  @doc """
  Subscribe to updates for a specific record.
  
  ## Examples
  
      iex> subscribe(:post, 123)
      :ok
  """
  def subscribe(topic, id) when is_atom(topic) and is_integer(id) do
    PubSub.subscribe(@pubsub, topic_name(topic, id))
  end
  
  @doc """
  Broadcast an event to all subscribers of a topic.
  
  ## Examples
  
      iex> broadcast(:posts, :posts_updated, %{posts: [...]})
      :ok
  """
  def broadcast(topic, event, payload) when is_atom(topic) and is_atom(event) do
    PubSub.broadcast(@pubsub, topic_name(topic), {event, payload})
  end
  
  @doc """
  Broadcast an event to all subscribers of a specific record.
  
  ## Examples
  
      iex> broadcast(:post, 123, :post_updated, %{post: post})
      :ok
  """
  def broadcast(topic, id, event, payload) when is_atom(topic) and is_integer(id) and is_atom(event) do
    PubSub.broadcast(@pubsub, topic_name(topic, id), {event, payload})
  end
  
  @doc """
  Broadcast an event to all subscribers of a topic, excluding the current process.
  
  ## Examples
  
      iex> broadcast_from_self(:posts, :posts_updated, %{posts: [...]})
      :ok
  """
  def broadcast_from_self(topic, event, payload) when is_atom(topic) and is_atom(event) do
    PubSub.broadcast_from(@pubsub, self(), topic_name(topic), {event, payload})
  end
  
  @doc """
  Broadcast an event to all subscribers of a specific record, excluding the current process.
  
  ## Examples
  
      iex> broadcast_from_self(:post, 123, :post_updated, %{post: post})
      :ok
  """
  def broadcast_from_self(topic, id, event, payload) when is_atom(topic) and is_integer(id) and is_atom(event) do
    PubSub.broadcast_from(@pubsub, self(), topic_name(topic, id), {event, payload})
  end
  
  # Private helper to generate consistent topic names
  defp topic_name(topic) when is_atom(topic) do
    "#{topic}"
  end
  
  defp topic_name(topic, id) when is_atom(topic) and is_integer(id) do
    "#{topic}:#{id}"
  end
end
