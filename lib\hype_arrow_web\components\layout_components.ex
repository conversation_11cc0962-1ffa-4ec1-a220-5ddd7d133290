defmodule HypeArrowWeb.LayoutComponents do
  @moduledoc """
  Provides layout UI components for the application.

  These components handle the overall layout structure including navbar,
  sidebar, and main content areas.
  """
  use Phoenix.Component
  use HypeArrowWeb, :verified_routes

  # Import components
  import HypeArrowWeb.CoreComponents

  @doc """
  Renders the application layout with navbar, sidebar, and main content.

  ## Examples

      <.app_layout>
        <:sidebar_items>
          <.sidebar_item icon="hero-home" active={true}>Home</.sidebar_item>
          <.sidebar_item icon="hero-magnifying-glass">Explore</.sidebar_item>
        </:sidebar_items>

        <:main_content>
          Your main content here
        </:main_content>
      </.app_layout>
  """
  attr :id, :string, default: "app-container"
  attr :class, :string, default: nil
  attr :current_user, :map, default: nil

  slot :sidebar_items, required: true
  slot :main_content, required: true

  def app_layout(assigns) do
    ~H"""
    <div id={@id} class={["flex flex-col h-screen", @class]} phx-hook="MobileMenu">
      <script>
        // Ensure toggleMobileMenu is available immediately
        if (typeof window.toggleMobileMenu !== 'function') {
          window.toggleMobileMenu = function(event) {
            if (event) {
              event.preventDefault();
              event.stopPropagation();
            }

            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (sidebar && overlay) {
              const isOpen = sidebar.classList.contains('open');

              if (isOpen) {
                sidebar.classList.remove('open');
                overlay.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
              } else {
                sidebar.classList.add('open');
                overlay.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
              }
            }
          };
        }

        // Global variable to store scroll position
        window.lastScrollPosition = 0;

        // Save scroll position when clicking on a post
        document.addEventListener('click', function(event) {
          // Check if the click is on a post card or its children
          const postCard = event.target.closest('[id^="post-"]');
          if (postCard && postCard.getAttribute('data-expanded') === 'false') {
            // Get the main content container's scroll position
            const mainContent = document.querySelector('.hype-main-content');
            const scrollPos = mainContent ? mainContent.scrollTop : 0;

            // Save the current scroll position
            window.lastScrollPosition = Number(scrollPos);
            console.log('Saved scroll position:', window.lastScrollPosition);

            // Also save to our dedicated scroll positions object if it exists
            if (window.scrollPositions) {
              const postId = postCard.id.replace('post-', '');
              window.scrollPositions.beforeExpand = Number(scrollPos);
              window.scrollPositions.byPostId[postId] = Number(scrollPos);
              console.log(`Also saved to scrollPositions for post ${postId}:`, scrollPos);

              // Debug log all saved positions
              console.log('All saved positions:', {
                lastScrollPosition: window.lastScrollPosition,
                beforeExpand: window.scrollPositions.beforeExpand,
                byPostId: window.scrollPositions.byPostId
              });
            }
          }
        });
      </script>

      <.navbar current_user={@current_user} />

      <div class="flex flex-1 overflow-hidden">
        <!-- Mobile Sidebar Overlay -->
        <div id="sidebar-overlay" class="hidden md:hidden"></div>
        
    <!-- Sidebar -->
        <aside id="sidebar" class="hype-sidebar md:block">
          <div class="py-6">
            <nav class="space-y-1 px-2">
              {render_slot(@sidebar_items)}
            </nav>
          </div>
        </aside>
        
    <!-- Main Content -->
        <main class="hype-main-content md:ml-64 overflow-y-auto w-full">
          <div class="hype-container">
            {render_slot(@main_content)}
          </div>
        </main>
      </div>
    </div>
    """
  end

  @doc """
  Renders the top navigation bar.

  ## Examples

      <.navbar current_user={@current_user} />
  """
  attr :class, :string, default: nil
  attr :current_user, :map, default: nil

  def navbar(assigns) do
    ~H"""
    <nav class={["hype-navbar", @class]}>
      <div class="flex items-center justify-between h-16">
        <!-- Left side - Mobile menu button and Brand -->
        <div class="flex items-center">
          <!-- Mobile menu button -->
          <button
            id="mobile-menu-button"
            class="md:hidden p-2 ml-2 text-gray-300 hover:text-white focus:outline-none"
            type="button"
            phx-click="ignore"
            onclick="toggleMobileMenu(event)"
          >
            <.icon name="hero-bars-3" class="h-6 w-6" />
          </button>
          
    <!-- Brand -->
          <div class="flex items-center px-2 md:px-4">
            <img src="/images/logo.png" alt="HypeArrow Logo" class="h-10 w-auto md:h-12" />
            <span class="ml-2 text-lg md:text-xl font-bold bg-gradient-brand text-transparent bg-clip-text">
              HypeArrow
            </span>
          </div>
        </div>
        
    <!-- Right side - Actions -->
        <div class="flex items-center space-x-2 md:space-x-4 pr-2 md:pr-4">
          <%= if @current_user do %>
            <.link
              phx-click="open-new-post-modal"
              class="hype-btn hype-btn-primary hidden sm:inline-flex"
            >
              <.icon name="hero-plus" class="h-5 w-5 mr-1" /> New Post
            </.link>
            <!-- Mobile New Post button -->
            <.link
              phx-click="open-new-post-modal"
              class="p-2 text-gray-300 hover:text-white sm:hidden"
            >
              <.icon name="hero-plus" class="h-6 w-6" />
            </.link>
            <button class="p-2 text-gray-300 hover:text-white">
              <.icon name="hero-bell" class="h-6 w-6" />
            </button>
            <div class="relative">
              <button class="p-2 text-gray-300 hover:text-white flex items-center">
                <.icon name="hero-user-circle" class="h-6 w-6 mr-1" />
                <span class="hidden md:inline-block text-sm">{@current_user.username}</span>
              </button>
              <.link
                href={~p"/users/log_out"}
                method="delete"
                class="hype-btn hype-btn-secondary text-sm"
              >
                Log out
              </.link>
            </div>
          <% else %>
            <.link
              href={~p"/users/register"}
              class="hype-btn hype-btn-secondary hidden sm:inline-flex"
            >
              Sign up
            </.link>
            <.link href={~p"/users/log_in"} class="hype-btn hype-btn-primary">
              Log in
            </.link>
          <% end %>
        </div>
      </div>
    </nav>
    """
  end

  @doc """
  Renders a sidebar navigation item.

  ## Examples

      <.sidebar_item icon="hero-home" active={true}>Home</.sidebar_item>
      <.sidebar_item icon="hero-magnifying-glass">Explore</.sidebar_item>
  """
  attr :icon, :string, required: true
  attr :active, :boolean, default: false
  attr :href, :string, default: "#"
  attr :class, :string, default: nil

  slot :inner_block, required: true

  def sidebar_item(assigns) do
    ~H"""
    <a
      href={@href}
      class={[
        "hype-nav-link",
        @active && "hype-nav-link-active",
        @class
      ]}
    >
      <.icon name={@icon} class="mr-3 h-5 w-5" /> {render_slot(@inner_block)}
    </a>
    """
  end

  @doc """
  Renders a page header with title and optional subtitle.

  ## Examples

      <.page_header>
        Your Feed
        <:subtitle>Check out the latest posts from the community</:subtitle>
      </.page_header>
  """
  attr :class, :string, default: nil

  slot :inner_block, required: true
  slot :subtitle

  def page_header(assigns) do
    ~H"""
    <div class={["page-header-container mb-6", @class]}>
      <h1 class="text-2xl font-bold text-white">{render_slot(@inner_block)}</h1>
      <%= if @subtitle != [] do %>
        <p class="text-gray-400">{render_slot(@subtitle)}</p>
      <% end %>
    </div>
    """
  end
end
