defmodule HypeArrow.Repo.Migrations.CreatePosts do
  use Ecto.Migration

  def change do
    create table(:posts) do
      add :title, :string, null: false
      add :content, :text, null: false
      add :likes_count, :integer, default: 0, null: false
      add :published_at, :utc_datetime, null: false

      timestamps(type: :utc_datetime)
    end

    # Add an index for faster querying by published_at
    create index(:posts, [:published_at])
  end
end
