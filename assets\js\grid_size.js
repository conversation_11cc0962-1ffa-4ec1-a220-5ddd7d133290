// Grid size persistence functionality
const setupGridSizePersistence = () => {
  // Load saved grid size from localStorage
  const loadGridSize = () => {
    try {
      const savedSize = localStorage.getItem('hypeArrow_gridSize');
      if (savedSize) {
        return parseInt(savedSize, 10);
      }
    } catch (e) {
      console.error('Error loading grid size from localStorage:', e);
    }
    return null;
  };

  // Save grid size to localStorage
  const saveGridSize = (size) => {
    try {
      localStorage.setItem('hypeArrow_gridSize', size.toString());
      console.log(`Grid size ${size} saved to localStorage`);
    } catch (e) {
      console.error('Error saving grid size to localStorage:', e);
    }
  };

  // Update active state of grid size buttons
  const updateActiveButton = (size) => {
    // Find all grid size buttons
    const buttons = document.querySelectorAll('.grid-size-btn');

    // Remove active class from all buttons
    buttons.forEach(btn => {
      btn.classList.remove('active');
    });

    // Find the button for the selected size and add active class
    const targetButton = Array.from(buttons).find(btn => {
      const sizeAttr = btn.getAttribute('phx-value-size');
      return sizeAttr && parseInt(sizeAttr, 10) === size;
    });

    if (targetButton) {
      targetButton.classList.add('active');
    }
  };

  // Initialize grid size from localStorage on page load
  const initializeGridSize = () => {
    const savedSize = loadGridSize();
    if (savedSize) {
      console.log(`Loaded saved grid size: ${savedSize}`);

      // Find the grid size control buttons
      const buttons = document.querySelectorAll('.grid-size-btn');
      if (buttons.length > 0) {
        // Find the button for the saved size
        const targetButton = Array.from(buttons).find(btn => {
          const sizeAttr = btn.getAttribute('phx-value-size');
          return sizeAttr && parseInt(sizeAttr, 10) === savedSize;
        });

        // Click the button to apply the saved size
        if (targetButton) {
          console.log('Clicking button to apply saved grid size');
          targetButton.click();
          updateActiveButton(savedSize);
        }
      }
    }
  };

  // Listen for grid size changes
  document.addEventListener('click', (e) => {
    const button = e.target.closest('.grid-size-btn');
    if (button) {
      const size = button.getAttribute('phx-value-size');
      if (size) {
        const sizeInt = parseInt(size, 10);
        saveGridSize(sizeInt);
        updateActiveButton(sizeInt);
      }
    }
  });

  // Initialize when the DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeGridSize);
  } else {
    // DOM already loaded, initialize now
    initializeGridSize();
  }
};

export default setupGridSizePersistence;
