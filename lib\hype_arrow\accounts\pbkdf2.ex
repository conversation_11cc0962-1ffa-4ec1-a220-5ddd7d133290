defmodule Pbkdf2 do
  @moduledoc """
  A simple implementation of PBKDF2 for password hashing.
  
  This is a simplified version for demonstration purposes.
  In a production environment, you should use a proper library like bcrypt_elixir.
  """
  
  @iterations 160_000
  @salt_length 16
  @hash_length 32
  
  @doc """
  Hashes a password using PBKDF2.
  """
  def hash_pwd_salt(password) when is_binary(password) do
    salt = :crypto.strong_rand_bytes(@salt_length)
    hash = hash_password(password, salt, @iterations, @hash_length)
    
    # Format: iterations:salt:hash
    "$pbkdf2-sha256$#{@iterations}$#{Base.encode16(salt, case: :lower)}$#{Base.encode16(hash, case: :lower)}"
  end
  
  @doc """
  Verifies a password against a hash.
  """
  def verify_pass(password, hash) when is_binary(password) and is_binary(hash) do
    case parse_hash(hash) do
      {:ok, iterations, salt, stored_hash} ->
        calculated_hash = hash_password(password, salt, iterations, byte_size(stored_hash))
        Plug.Crypto.secure_compare(calculated_hash, stored_hash)
      _ ->
        false
    end
  end
  
  @doc """
  A dummy verify function to prevent timing attacks.
  """
  def no_user_verify do
    # Simulate the same amount of work as verify_pass
    hash_password("password", :crypto.strong_rand_bytes(@salt_length), @iterations, @hash_length)
    false
  end
  
  # Parse a hash string into its components
  defp parse_hash(hash) do
    case String.split(hash, "$", trim: true) do
      ["pbkdf2-sha256", iterations, salt, hash_part] ->
        {iterations, _} = Integer.parse(iterations)
        salt = Base.decode16!(salt, case: :lower)
        hash_part = Base.decode16!(hash_part, case: :lower)
        {:ok, iterations, salt, hash_part}
      _ ->
        :error
    end
  end
  
  # Hash a password with the given salt, iterations, and output length
  defp hash_password(password, salt, iterations, length) do
    :crypto.pbkdf2_hmac(:sha256, password, salt, iterations, length)
  end
end
