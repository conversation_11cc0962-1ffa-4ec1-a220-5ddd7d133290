defmodule HypeArrowWeb.ModalComponents do
  @moduledoc """
  Provides modal UI components for the application.

  These components handle various modal dialogs used throughout the application.
  """
  use Phoenix.Component
  use HypeArrowWeb, :verified_routes

  # Import components
  import HypeArrowWeb.Components.FormComponents

  @doc """
  Renders a new post modal.

  ## Examples

      <.new_post_modal
        show={@show_new_post_modal}
        form={@new_post_form}
      />
  """
  attr :show, :boolean, required: true
  attr :form, :any, required: true
  attr :class, :string, default: nil

  def new_post_modal(assigns) do
    ~H"""
    <div
      id="custom-modal"
      class={
        (@show &&
           "fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50") || "hidden"
      }
    >
      <div class="bg-background-dark w-full max-w-md mx-auto rounded-lg shadow-lg overflow-hidden border border-brand-light">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold text-white">Create New Post</h2>
            <button type="button" phx-click="close-modal" class="text-gray-400 hover:text-white">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <.form
            for={@form}
            phx-submit="save-new-post"
            phx-change="validate-new-post"
            class="space-y-4"
          >
            <.form_field form={@form} field={:title} label="Title" required={true}>
              <.styled_input
                field={@form[:title]}
                placeholder="Enter a title for your post"
                required={true}
                phx-debounce="300"
              />
            </.form_field>

            <.form_field form={@form} field={:content} label="Content" required={true}>
              <.styled_textarea
                field={@form[:content]}
                placeholder="What's on your mind?"
                required={true}
                rows={6}
                phx-debounce="300"
              />
            </.form_field>

            <div class="flex justify-end space-x-3 mt-6">
              <button type="button" phx-click="close-modal" class="hype-btn hype-btn-secondary">
                Cancel
              </button>
              <button type="submit" class="hype-btn hype-btn-primary">
                Post
              </button>
            </div>
          </.form>
        </div>
      </div>
    </div>
    """
  end
end
