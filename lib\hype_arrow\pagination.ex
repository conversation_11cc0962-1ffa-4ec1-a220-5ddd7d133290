defmodule HypeArrow.Pagination do
  @moduledoc """
  Provides pagination functionality for Ecto queries.
  
  This module can be used to paginate any Ecto query with cursor-based pagination,
  which is ideal for infinite scrolling.
  """
  
  import Ecto.Query
  
  @default_limit 10
  
  @doc """
  Paginates a query using cursor-based pagination.
  
  ## Parameters
  
  * `query` - The Ecto query to paginate
  * `cursor_field` - The field to use as the cursor (default: :inserted_at)
  * `cursor_value` - The value of the cursor to start from (default: nil)
  * `direction` - The direction to paginate (:after or :before, default: :after)
  * `limit` - The maximum number of records to return (default: 10)
  * `order` - The order to sort results (:desc or :asc, default: :desc)
  
  ## Returns
  
  A tuple containing:
  * The modified query with pagination applied
  * A map with pagination metadata:
    * `:has_more` - Boolean indicating if there are more records
    * `:next_cursor` - The cursor value for the next page
    * `:prev_cursor` - The cursor value for the previous page
  
  ## Examples
  
      iex> query = from(p in Post)
      iex> {paginated_query, meta} = Pagination.paginate_query(query, :inserted_at, nil, :after, 10, :desc)
      iex> posts = Repo.all(paginated_query)
      iex> next_cursor = meta.next_cursor
  """
  def paginate_query(query, cursor_field \\ :inserted_at, cursor_value \\ nil, direction \\ :after, limit \\ @default_limit, order \\ :desc) do
    limit = limit + 1  # Fetch one extra record to determine if there are more records
    
    # Apply cursor condition if cursor_value is provided
    query = if cursor_value do
      case {direction, order} do
        {:after, :desc} -> 
          query |> where([q], field(q, ^cursor_field) < ^cursor_value)
        {:after, :asc} -> 
          query |> where([q], field(q, ^cursor_field) > ^cursor_value)
        {:before, :desc} -> 
          query |> where([q], field(q, ^cursor_field) > ^cursor_value)
        {:before, :asc} -> 
          query |> where([q], field(q, ^cursor_field) < ^cursor_value)
      end
    else
      query
    end
    
    # Apply ordering
    query = case order do
      :desc -> query |> order_by([q], desc: ^cursor_field)
      :asc -> query |> order_by([q], asc: ^cursor_field)
    end
    
    # Apply limit
    query = query |> limit(^limit)
    
    # Return the query and metadata for pagination
    {query, %{
      limit: limit - 1,  # Actual limit without the extra record
      has_more: false,   # Will be updated after query execution
      next_cursor: nil,  # Will be updated after query execution
      prev_cursor: cursor_value
    }}
  end
  
  @doc """
  Processes the results of a paginated query to determine pagination metadata.
  
  ## Parameters
  
  * `results` - The results from the paginated query
  * `meta` - The pagination metadata from `paginate_query/6`
  * `cursor_field` - The field used as the cursor
  
  ## Returns
  
  A tuple containing:
  * The results without the extra record used to determine if there are more records
  * Updated pagination metadata
  
  ## Examples
  
      iex> {query, meta} = Pagination.paginate_query(query)
      iex> results = Repo.all(query)
      iex> {posts, updated_meta} = Pagination.process_results(results, meta, :inserted_at)
  """
  def process_results(results, meta, cursor_field) do
    has_more = length(results) > meta.limit
    
    # Remove the extra record if there are more records
    results = if has_more, do: Enum.take(results, meta.limit), else: results
    
    # Get the cursor value for the next page
    next_cursor = case List.last(results) do
      nil -> nil
      last -> Map.get(last, cursor_field)
    end
    
    # Update metadata
    meta = Map.merge(meta, %{
      has_more: has_more,
      next_cursor: next_cursor
    })
    
    {results, meta}
  end
end
