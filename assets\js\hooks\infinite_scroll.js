// Infinite scroll hook for LiveView
const InfiniteScroll = {
  page() {
    return this.el.dataset.page;
  },

  loadingElement() {
    return document.getElementById(`${this.el.id}-loading`);
  },

  triggerElement() {
    return document.getElementById(`${this.el.id}-trigger`);
  },

  endElement() {
    return document.getElementById(`${this.el.id}-end`);
  },

  observer: null,

  mounted() {
    this.setupInfiniteScroll();
  },

  updated() {
    this.setupInfiniteScroll();
  },

  setupInfiniteScroll() {
    const trigger = this.triggerElement();
    const endEl = this.endElement();
    const loadingEl = this.loadingElement();

    // Check if we've reached the end of results
    // Convert the string value to a boolean
    const hasMoreStr = this.el.dataset.hasMore;
    const hasMore = hasMoreStr === 'true' || hasMoreStr === true;

    // Debug logging
    console.log('Infinite scroll setup - has_more:', hasMoreStr, 'converted to:', hasMore);

    // If we've reached the end, disconnect the observer and show the end message
    if (!hasMore) {
      console.log('No more results to load, disconnecting observer');

      if (this.observer) {
        this.observer.disconnect();
        this.observer = null;
      }

      // Hide the loading element and trigger
      if (loadingEl) loadingEl.style.display = 'none';
      if (trigger) trigger.style.display = 'none';

      // Show the end element
      if (endEl) endEl.style.display = 'block';

      return;
    }

    // If we still have more results, set up the observer
    if (!trigger) return;

    // Disconnect any existing observer
    if (this.observer) {
      this.observer.disconnect();
    }

    // Create a new IntersectionObserver
    this.observer = new IntersectionObserver(entries => {
      const entry = entries[0];
      if (entry.isIntersecting) {
        this.loadMore();
      }
    }, {
      root: null, // Use the viewport
      rootMargin: '0px 0px 200px 0px', // Load more when within 200px of the bottom
      threshold: 0.1 // Trigger when at least 10% of the element is visible
    });

    // Start observing the trigger element
    this.observer.observe(trigger);

    // Show the trigger and loading elements, hide the end element
    if (trigger) trigger.style.display = 'block';
    if (loadingEl) loadingEl.style.display = 'block';
    if (endEl) endEl.style.display = 'none';
  },

  loadMore() {
    const loadingEl = this.loadingElement();
    const endEl = this.endElement();

    // Don't load more if already loading
    if (loadingEl && loadingEl.classList.contains('loading')) return;

    // Don't load more if we've reached the end
    // This is a client-side check in addition to the server-side check
    if (endEl && endEl.style.display !== 'none') {
      console.log('End of results reached, not loading more');
      return;
    }

    // Add loading class
    if (loadingEl) {
      loadingEl.classList.add('loading');
    }

    // Get the event name from data attribute
    const event = this.el.dataset.pageEvent || 'load-more';

    // Push the event to the server
    this.pushEvent(event, {});

    // Log the load more action
    console.log(`Loading more content with event: ${event}`);
  },

  destroyed() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
};

export default InfiniteScroll;
