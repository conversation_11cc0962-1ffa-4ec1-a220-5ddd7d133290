/* Infinite scroll styles */
.infinite-scroll-container {
  position: relative;
}

.infinite-scroll-trigger {
  height: 20px;
  width: 100%;
  margin-top: 1rem;
}

.infinite-scroll-loading {
  width: 100%;
  padding: 1rem 0;
  text-align: center;
}

.infinite-scroll-end {
  width: 100%;
  padding: 1rem 0;
  text-align: center;
  color: rgba(156, 163, 175, 0.8);
  font-size: 0.875rem;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.infinite-scroll-loading .animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
