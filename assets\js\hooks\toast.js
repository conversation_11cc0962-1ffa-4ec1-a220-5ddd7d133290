// Toast notification hook for LiveView
const Toast = {
  mounted() {
    this.setupToast();
  },

  updated() {
    this.setupToast();
  },

  setupToast() {
    // Get the close after duration from data attribute
    const closeAfter = parseInt(this.el.dataset.closeAfter, 10) || 5000;
    
    // Set up auto-close timer
    if (closeAfter > 0) {
      // Clear any existing timer
      if (this.timer) {
        clearTimeout(this.timer);
      }
      
      // Set a new timer
      this.timer = setTimeout(() => {
        this.closeToast();
      }, closeAfter);
    }
    
    // Add click event listener to close button
    const closeButton = this.el.querySelector('button');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        this.closeToast();
      });
    }
  },
  
  closeToast() {
    // Add closing animation
    this.el.classList.add('translate-x-full', 'opacity-0');
    
    // Remove the element after animation completes
    setTimeout(() => {
      this.el.remove();
    }, 300);
    
    // Clear the timer
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  
  destroyed() {
    // Clear the timer when the element is removed
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
};

export default Toast;
