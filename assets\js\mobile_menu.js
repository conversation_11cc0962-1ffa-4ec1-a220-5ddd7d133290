// Global function to toggle the mobile menu
window.toggleMobileMenu = function (event) {
  console.log("toggleMobileMenu called");
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }

  const sidebar = document.getElementById('sidebar');
  const overlay = document.getElementById('sidebar-overlay');

  if (!sidebar || !overlay) {
    console.error("Sidebar elements not found");
    return;
  }

  console.log("Toggling sidebar");

  // Check if sidebar is currently open
  const isOpen = sidebar.classList.contains('open');

  if (isOpen) {
    // Close the sidebar
    sidebar.classList.remove('open');
    overlay.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    console.log("Sidebar closed");
  } else {
    // Open the sidebar
    sidebar.classList.add('open');
    overlay.classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
    console.log("Sidebar opened");
  }
};

// Mobile menu functionality
const setupMobileMenu = () => {
  console.log("Setting up mobile menu...");

  // Get the hamburger button
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const sidebar = document.getElementById('sidebar');
  const overlay = document.getElementById('sidebar-overlay');

  console.log("Elements:", { mobileMenuButton, sidebar, overlay });

  if (!sidebar || !overlay) {
    console.error("Mobile menu elements not found!");
    return;
  }

  // Make sure the sidebar is initially hidden on mobile
  if (window.innerWidth < 768) {
    sidebar.classList.remove('open');
    overlay.classList.add('hidden');
  }

  // Add click event to hamburger button directly
  if (mobileMenuButton) {
    mobileMenuButton.addEventListener('click', (e) => {
      console.log("Menu button clicked via event listener");
      window.toggleMobileMenu(e);
    });
  }

  // Close sidebar when overlay is clicked
  overlay.addEventListener('click', (e) => {
    e.preventDefault();
    window.toggleMobileMenu(e);
  });

  // Close sidebar when escape key is pressed
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && sidebar.classList.contains('open')) {
      window.toggleMobileMenu(e);
    }
  });

  // Close sidebar when window is resized to desktop size
  window.addEventListener('resize', () => {
    if (window.innerWidth >= 768 && sidebar.classList.contains('open')) {
      sidebar.classList.remove('open');
      overlay.classList.add('hidden');
      document.body.classList.remove('overflow-hidden');
    }
  });

  console.log("Mobile menu setup complete");
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', setupMobileMenu);

export default setupMobileMenu;
