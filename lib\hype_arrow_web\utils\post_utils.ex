defmodule HypeArrowWeb.Utils.PostUtils do
  @moduledoc """
  Utility functions for post operations.
  
  This module provides helper functions for common post operations like
  deduplication, position management, and other post-related utilities.
  """
  
  @doc """
  Ensures a post is at the top of the list.
  
  ## Examples
  
      iex> ensure_post_at_top(posts, post_id)
      [%Post{id: post_id}, ...]
  """
  def ensure_post_at_top(posts, post_id) when is_list(posts) and is_integer(post_id) do
    # Check if the post is already in the list
    post = Enum.find(posts, fn post -> post.id == post_id end)
    
    if post do
      # Remove the post from its current position
      filtered_posts = Enum.filter(posts, fn p -> p.id != post_id end)
      
      # Create a new list with the post at the beginning
      [post | filtered_posts]
    else
      # Post not found in the list, return the original list
      posts
    end
  end
  
  @doc """
  Ensures a post is at a specific position in the list.
  
  ## Examples
  
      iex> ensure_post_at_position(posts, post_id, 2)
      [%Post{}, %Post{id: post_id}, ...]
  """
  def ensure_post_at_position(posts, post_id, position) when is_list(posts) and is_integer(post_id) and is_integer(position) do
    # Check if the post is already in the list
    post = Enum.find(posts, fn post -> post.id == post_id end)
    
    if post do
      # Remove the post from its current position
      filtered_posts = Enum.filter(posts, fn p -> p.id != post_id end)
      
      # Ensure position is within bounds
      position = max(0, min(position, length(filtered_posts)))
      
      # Insert the post at the specified position
      List.insert_at(filtered_posts, position, post)
    else
      # Post not found in the list, return the original list
      posts
    end
  end
  
  @doc """
  Removes duplicates from a list of posts.
  
  ## Examples
  
      iex> deduplicate_posts(posts)
      [%Post{}, %Post{}, ...]
  """
  def deduplicate_posts(posts) when is_list(posts) do
    Enum.uniq_by(posts, fn post -> post.id end)
  end
  
  @doc """
  Merges two lists of posts, ensuring no duplicates.
  
  ## Examples
  
      iex> merge_posts(first_page, rest_of_posts)
      [%Post{}, %Post{}, ...]
  """
  def merge_posts(first_page, rest_of_posts) when is_list(first_page) and is_list(rest_of_posts) do
    # Get the IDs of posts in the first page
    first_page_ids = Enum.map(first_page, fn post -> post.id end)
    
    # Filter out any posts from rest_of_posts that are already in first_page
    filtered_rest = Enum.filter(rest_of_posts, fn post -> post.id not in first_page_ids end)
    
    # Combine the lists
    first_page ++ filtered_rest
  end
  
  @doc """
  Updates a post in a list of posts.
  
  ## Examples
  
      iex> update_post_in_list(posts, updated_post)
      [%Post{}, %Post{}, ...]
  """
  def update_post_in_list(posts, updated_post) when is_list(posts) and is_map(updated_post) do
    Enum.map(posts, fn post ->
      if post.id == updated_post.id do
        updated_post
      else
        post
      end
    end)
  end
  
  @doc """
  Removes a post from a list of posts.
  
  ## Examples
  
      iex> remove_post_from_list(posts, post_id)
      [%Post{}, %Post{}, ...]
  """
  def remove_post_from_list(posts, post_id) when is_list(posts) and is_integer(post_id) do
    Enum.filter(posts, fn post -> post.id != post_id end)
  end
end
