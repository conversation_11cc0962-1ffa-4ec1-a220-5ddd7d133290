defmodule HypeArrowWeb.Components.SocialComponents do
  @moduledoc """
  Provides UI components for social interactions.

  These components handle rendering of social action buttons and related elements.
  """
  use Phoenix.Component

  # Import components
  import HypeArrowWeb.Components.ButtonComponents

  @doc """
  Renders social action buttons (like, comment, share).

  ## Examples

      <.social_action_buttons likes_count={42} />
  """
  attr :likes_count, :integer, required: true
  attr :class, :string, default: nil

  def social_action_buttons(assigns) do
    ~H"""
    <div class={["flex flex-wrap mt-3 pt-3 border-t border-gray-800", @class]}>
      <.icon_button
        icon="hero-heart"
        variant="ghost"
        class="mr-4 mb-2 text-gray-400 hover:text-brand text-xs"
      >
        <span>{@likes_count}</span>
      </.icon_button>
      <.icon_button
        icon="hero-chat-bubble-left"
        variant="ghost"
        class="mr-4 mb-2 text-gray-400 hover:text-brand text-xs"
      >
        <span>Comments</span>
      </.icon_button>
      <.icon_button
        icon="hero-share"
        variant="ghost"
        class="mb-2 text-gray-400 hover:text-brand text-xs"
      >
        <span>Share</span>
      </.icon_button>
    </div>
    """
  end
end
