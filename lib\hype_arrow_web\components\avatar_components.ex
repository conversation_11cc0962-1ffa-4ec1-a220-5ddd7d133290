defmodule HypeArrowWeb.Components.AvatarComponents do
  @moduledoc """
  Provides avatar UI components for the application.

  These components handle rendering of user avatars with consistent appearance.
  """
  use Phoenix.Component

  # No imports needed

  @doc """
  Renders a user avatar.

  ## Examples

      <.avatar name="<PERSON>" />
      <.avatar name="<PERSON>" src="/images/avatars/john.jpg" size="lg" />
  """
  attr :name, :string, required: true
  attr :src, :string, default: nil
  attr :size, :string, default: "md", values: ~w(xs sm md lg xl)
  attr :status, :string, default: "", values: ["", "online", "away", "busy", "offline"]
  attr :class, :string, default: nil
  attr :rest, :global

  def avatar(assigns) do
    # Generate initials from name
    assigns =
      assign_new(assigns, :initials, fn ->
        assigns.name
        |> String.split(" ")
        |> Enum.map(fn name -> String.first(name) end)
        |> Enum.join("")
        |> String.slice(0..1)
        |> String.upcase()
      end)

    # Generate a consistent background color based on the name
    assigns =
      assign_new(assigns, :bg_color, fn ->
        hue = :erlang.phash2(assigns.name, 360)
        "hsl(#{hue}, 70%, 40%)"
      end)

    ~H"""
    <div
      class={
        [
          "relative inline-flex items-center justify-center rounded-full bg-gray-700 text-white overflow-hidden",
          # Size classes
          @size == "xs" && "h-6 w-6 text-xs",
          @size == "sm" && "h-8 w-8 text-sm",
          @size == "md" && "h-10 w-10 text-base",
          @size == "lg" && "h-12 w-12 text-lg",
          @size == "xl" && "h-16 w-16 text-xl",
          @class
        ]
      }
      style={if !@src, do: "background-color: #{@bg_color};"}
      {@rest}
    >
      <%= if @src do %>
        <img src={@src} alt={@name} class="h-full w-full object-cover" loading="lazy" />
      <% else %>
        <span class="font-medium">{@initials}</span>
      <% end %>

      <%= if @status != "" do %>
        <span class={
          [
            "absolute bottom-0 right-0 block rounded-full ring-2 ring-white",
            # Size classes
            @size == "xs" && "h-1.5 w-1.5",
            @size == "sm" && "h-2 w-2",
            @size == "md" && "h-2.5 w-2.5",
            @size == "lg" && "h-3 w-3",
            @size == "xl" && "h-4 w-4",
            # Status classes
            @status == "online" && "bg-green-400",
            @status == "away" && "bg-yellow-400",
            @status == "busy" && "bg-red-400",
            @status == "offline" && "bg-gray-400"
          ]
        }>
        </span>
      <% end %>
    </div>
    """
  end

  @doc """
  Renders a user avatar with name.

  ## Examples

      <.avatar_with_name name="John Doe" />
      <.avatar_with_name name="John Doe" src="/images/avatars/john.jpg" subtitle="Developer" />
  """
  attr :name, :string, required: true
  attr :src, :string, default: nil
  attr :subtitle, :string, default: nil
  attr :size, :string, default: "md", values: ~w(sm md lg)
  attr :status, :string, default: "", values: ["", "online", "away", "busy", "offline"]
  attr :class, :string, default: nil
  attr :rest, :global

  def avatar_with_name(assigns) do
    ~H"""
    <div class={["flex items-center", @class]} {@rest}>
      <.avatar name={@name} src={@src} size={@size} status={@status} class="flex-shrink-0" />
      <div class={
        [
          "ml-2 flex flex-col",
          # Size classes
          @size == "sm" && "space-y-0",
          @size == "md" && "space-y-0.5",
          @size == "lg" && "space-y-1"
        ]
      }>
        <span class={
          [
            "font-medium text-white",
            # Size classes
            @size == "sm" && "text-sm",
            @size == "md" && "text-base",
            @size == "lg" && "text-lg"
          ]
        }>
          {@name}
        </span>
        <%= if @subtitle do %>
          <span class={
            [
              "text-gray-400",
              # Size classes
              @size == "sm" && "text-xs",
              @size == "md" && "text-sm",
              @size == "lg" && "text-base"
            ]
          }>
            {@subtitle}
          </span>
        <% end %>
      </div>
    </div>
    """
  end

  @doc """
  Renders a group of avatars.

  ## Examples

      <.avatar_group users={[%{name: "John Doe"}, %{name: "Jane Smith"}]} />
      <.avatar_group users={users} max={3} size="lg" />
  """
  attr :users, :list, required: true
  attr :max, :integer, default: 5
  attr :size, :string, default: "md", values: ~w(xs sm md lg xl)
  attr :class, :string, default: nil

  def avatar_group(assigns) do
    # Calculate how many users to show and how many are remaining
    visible_users = Enum.take(assigns.users, assigns.max)
    remaining = max(length(assigns.users) - assigns.max, 0)

    assigns = assign(assigns, :visible_users, visible_users)
    assigns = assign(assigns, :remaining, remaining)

    ~H"""
    <div class={["flex -space-x-2", @class]}>
      <%= for user <- @visible_users do %>
        <.avatar
          name={user.name}
          src={Map.get(user, :src)}
          size={@size}
          class="ring-2 ring-background-dark"
        />
      <% end %>

      <%= if @remaining > 0 do %>
        <div class={
          [
            "flex items-center justify-center rounded-full bg-gray-700 text-white ring-2 ring-background-dark",
            # Size classes
            @size == "xs" && "h-6 w-6 text-xs",
            @size == "sm" && "h-8 w-8 text-sm",
            @size == "md" && "h-10 w-10 text-base",
            @size == "lg" && "h-12 w-12 text-lg",
            @size == "xl" && "h-16 w-16 text-xl"
          ]
        }>
          <span class="font-medium">+{@remaining}</span>
        </div>
      <% end %>
    </div>
    """
  end
end
