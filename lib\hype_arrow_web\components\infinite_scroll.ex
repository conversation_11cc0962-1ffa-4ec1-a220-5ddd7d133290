defmodule HypeArrowWeb.Components.InfiniteScroll do
  @moduledoc """
  Provides UI components for infinite scrolling.

  This component can be used to implement infinite scrolling in any LiveView.
  """
  use Phoenix.Component

  @doc """
  Renders an infinite scroll container.

  ## Examples

      <.infinite_scroll
        id="posts-infinite-scroll"
        page_info={@page_info}
        load_more="load-more-posts"
        end_of_results_message="No more posts to load"
      >
        <div class="posts-grid">
          <%= for post <- @posts do %>
            <.post_card post={post} />
          <% end %>
        </div>
      </.infinite_scroll>
  """
  attr :id, :string, required: true
  attr :page_info, :map, required: true
  attr :load_more, :string, required: true
  attr :end_of_results_message, :string, default: "No more results to load"
  attr :loading_message, :string, default: "Loading more..."
  attr :class, :string, default: nil
  slot :inner_block, required: true

  def infinite_scroll(assigns) do
    ~H"""
    <div
      id={@id}
      class={["infinite-scroll-container", @class]}
      phx-hook="InfiniteScroll"
      data-page-event={@load_more}
      data-has-more={to_string(@page_info.has_more)}
    >
      {render_slot(@inner_block)}

      <div class="infinite-scroll-status">
        <%= if @page_info.has_more do %>
          <div class="infinite-scroll-trigger" id={"#{@id}-trigger"} phx-update="ignore">
            <!-- This element is used as the intersection observer target -->
          </div>
          <div class="infinite-scroll-loading" id={"#{@id}-loading"}>
            <div class="flex items-center justify-center py-4">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-brand"></div>
              <span class="ml-2 text-gray-400">{@loading_message}</span>
            </div>
          </div>
        <% else %>
          <div class="infinite-scroll-end" id={"#{@id}-end"}>
            <div class="text-center py-4 text-gray-400 text-sm">
              {@end_of_results_message}
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
