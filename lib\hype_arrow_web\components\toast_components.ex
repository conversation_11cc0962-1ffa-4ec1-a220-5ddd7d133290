defmodule HypeArrowWeb.Components.ToastComponents do
  @moduledoc """
  Provides toast notification components for the application.

  These components handle rendering of toast notifications with consistent appearance.
  """
  use Phoenix.Component

  # Import components
  import HypeArrowWeb.CoreComponents
  alias Phoenix.LiveView.JS

  @doc """
  Renders a toast notification.

  ## Examples

      <.toast
        id="success-toast"
        type="success"
        title="Success!"
        message="Your changes have been saved."
        show={true}
      />
  """
  attr :id, :string, required: true
  attr :type, :string, default: "info", values: ~w(info success warning error)
  attr :title, :string, default: nil
  attr :message, :string, required: true
  attr :show, :boolean, default: true
  attr :close_after, :integer, default: 5000
  attr :class, :string, default: nil
  attr :on_close, :any, default: %{}

  def toast(assigns) do
    ~H"""
    <div
      id={@id}
      class={
        [
          "fixed right-4 top-4 z-50 w-72 transform transition-all duration-300 ease-in-out",
          "rounded-lg shadow-lg backdrop-blur-sm",
          # Type classes
          @type == "info" && "bg-blue-900/80 border border-blue-700",
          @type == "success" && "bg-green-900/80 border border-green-700",
          @type == "warning" && "bg-yellow-900/80 border border-yellow-700",
          @type == "error" && "bg-red-900/80 border border-red-700",
          # Show/hide classes
          @show && "translate-x-0 opacity-100",
          !@show && "translate-x-full opacity-0",
          @class
        ]
      }
      phx-hook="Toast"
      data-close-after={@close_after}
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <%= case @type do %>
              <% "info" -> %>
                <.icon name="hero-information-circle" class="h-5 w-5 text-blue-400" />
              <% "success" -> %>
                <.icon name="hero-check-circle" class="h-5 w-5 text-green-400" />
              <% "warning" -> %>
                <.icon name="hero-exclamation-triangle" class="h-5 w-5 text-yellow-400" />
              <% "error" -> %>
                <.icon name="hero-x-circle" class="h-5 w-5 text-red-400" />
            <% end %>
          </div>
          <div class="ml-3 w-0 flex-1">
            <%= if @title do %>
              <p class="text-sm font-medium text-white">
                {@title}
              </p>
            <% end %>
            <p class={["text-sm text-gray-200", !@title && "mt-0.5"]}>
              {@message}
            </p>
          </div>
          <div class="ml-4 flex flex-shrink-0">
            <button
              type="button"
              class="inline-flex rounded-md text-gray-400 hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              phx-click={@on_close}
            >
              <span class="sr-only">Close</span>
              <.icon name="hero-x-mark" class="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders a toast container that manages multiple toasts.

  ## Examples

      <.toast_container flash={@flash} />
  """
  attr :flash, :map, required: true

  def toast_container(assigns) do
    ~H"""
    <div id="toast-container" class="fixed right-0 top-0 z-50 p-4 space-y-4">
      <%= for {type, message} <- @flash, message != nil, type in ~w(info success warning error) do %>
        <.toast
          id={"flash-#{type}"}
          type={type}
          message={message}
          show={true}
          on_close={JS.push("lv:clear-flash", value: %{key: type})}
        />
      <% end %>
    </div>
    """
  end
end
