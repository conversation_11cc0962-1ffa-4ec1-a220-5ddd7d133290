<div class="mx-auto max-w-2xl">
  <.header class="text-center">
    Account Settings
    <:subtitle>Manage your account email address and password settings</:subtitle>
  </.header>

  <div class="space-y-12 divide-y">
    <div>
      <.simple_form
        :let={f}
        for={@email_changeset}
        action={~p"/users/settings"}
        id="update_email"
        method="put"
      >
        <input type="hidden" name="action" value="update_email" />
        <.error :if={@email_changeset.action}>
          Oops, something went wrong! Please check the errors below.
        </.error>

        <.input field={f[:email]} type="email" label="Email" required />
        <.input
          field={f[:current_password]}
          name="current_password"
          id="current_password_for_email"
          type="password"
          label="Current password"
          required
        />
        <:actions>
          <.button phx-disable-with="Changing...">Change Email</.button>
        </:actions>
      </.simple_form>
    </div>
    <div>
      <.simple_form
        :let={f}
        for={@password_changeset}
        action={~p"/users/settings"}
        id="update_password"
        method="put"
      >
        <input type="hidden" name="action" value="update_password" />
        <.error :if={@password_changeset.action}>
          Oops, something went wrong! Please check the errors below.
        </.error>

        <.input field={f[:password]} type="password" label="New password" required />
        <.input field={f[:password_confirmation]} type="password" label="Confirm new password" />
        <.input
          field={f[:current_password]}
          name="current_password"
          type="password"
          label="Current password"
          id="current_password_for_password"
          required
        />
        <:actions>
          <.button phx-disable-with="Changing...">Change Password</.button>
        </:actions>
      </.simple_form>
    </div>
  </div>
</div>
