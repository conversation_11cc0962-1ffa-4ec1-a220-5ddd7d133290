<.new_post_modal show={@show_new_post_modal} form={@new_post_form} />
<.edit_post_modal show={@show_edit_post_modal} post={@edit_post} on_save="save-edit-post" />

<.app_layout current_user={@current_user}>
  <:sidebar_items>
    <.sidebar_item icon="hero-home" active={true}>Home</.sidebar_item>
    <.sidebar_item icon="hero-magnifying-glass">Explore</.sidebar_item>
    <.sidebar_item icon="hero-bell">Notifications</.sidebar_item>
    <.sidebar_item icon="hero-envelope">Messages</.sidebar_item>
    <.sidebar_item icon="hero-bookmark">Bookmarks</.sidebar_item>
    <.sidebar_item icon="hero-user">Profile</.sidebar_item>
    <.sidebar_item icon="hero-cog-6-tooth">Settings</.sidebar_item>
  </:sidebar_items>

  <:main_content>
    <.page_header>
      Your Feed
      <:subtitle>Check out the latest posts from the community</:subtitle>
    </.page_header>

    <.grid_size_controls grid_size={@grid_size} />

    <div id="post-edit-scroll-container" phx-hook="PostEditScroll">
      <.infinite_scroll
        id="posts-infinite-scroll"
        page_info={@page_info}
        load_more="load-more-posts"
        end_of_results_message="No more posts to load"
      >
        <.post_grid
          posts={@posts}
          expanded_post_id={@expanded_post_id}
          on_toggle={&toggle_post(%JS{}, &1)}
          grid_size={@grid_size}
          current_user={@current_user}
          editing_post_id={@editing_post_id}
          edit_form={@edit_form}
        />
      </.infinite_scroll>
    </div>
  </:main_content>
</.app_layout>
