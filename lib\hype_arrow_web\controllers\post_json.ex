defmodule HypeArrowWeb.PostJSON do
  alias HypeArrow.Social.Post

  @doc """
  Renders a list of posts.
  """
  def index(%{posts: posts}) do
    %{data: for(post <- posts, do: data(post))}
  end

  @doc """
  Renders a single post.
  """
  def show(%{post: post}) do
    %{data: data(post)}
  end

  @doc """
  Renders an error for invalid changeset.
  """
  def error(%{changeset: changeset}) do
    # When encoded, the changeset returns its errors
    # as a JSON object. So we just pass it forward.
    %{errors: Ecto.Changeset.traverse_errors(changeset, &translate_error/1)}
  end

  defp translate_error({msg, opts}) do
    # You can make use of gettext to translate error messages by
    # uncommenting and adjusting the following code:

    # if count = opts[:count] do
    #   Gettext.dngettext(HypeArrowWeb.Gettext, "errors", msg, msg, count, opts)
    # else
    #   Gettext.dgettext(HypeArrowWeb.Gettext, "errors", msg, opts)
    # end

    Enum.reduce(opts, msg, fn {key, value}, acc ->
      String.replace(acc, "%{#{key}}", fn _ -> to_string(value) end)
    end)
  end

  defp data(%Post{} = post) do
    %{
      id: post.id,
      title: post.title,
      content: post.content,
      likes_count: post.likes_count,
      published_at: post.published_at,
      inserted_at: post.inserted_at,
      updated_at: post.updated_at,
      user: user_data(post.user)
    }
  end

  defp user_data(nil), do: nil

  defp user_data(user) do
    %{
      id: user.id,
      username: user.username,
      email: user.email
    }
  end
end
