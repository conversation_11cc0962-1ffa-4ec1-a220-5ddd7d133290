defmodule HypeArrowWeb.Components.CardComponents do
  @moduledoc """
  Provides card UI components for the application.

  These components handle rendering of various card styles with consistent appearance.
  """
  use Phoenix.Component

  @doc """
  Renders a generic card container that can be used for any type of content.

  ## Examples

      <.card_container
        id="item-123"
        index={0}
        expanded={false}
        on_click={on_toggle_function.(123)}
      >
        <:header>
          <div>Header Content</div>
        </:header>
        <:body>
          <div>Body Content</div>
        </:body>
        <:footer>
          <div>Footer Content</div>
        </:footer>
      </.card_container>
  """
  attr :id, :string, required: true
  attr :index, :integer, required: true
  attr :expanded, :boolean, default: false
  attr :on_click, :any, default: nil
  attr :class, :string, default: nil
  attr :header_class, :string, default: nil
  attr :body_class, :string, default: nil
  attr :footer_class, :string, default: nil
  attr :rest, :global, include: ~w(data-index data-expanded)

  slot :header, required: true
  slot :body, required: true
  slot :footer, required: true

  def card_container(assigns) do
    ~H"""
    <div
      id={@id}
      class={[
        "card-container",
        @expanded && "expanded-card",
        @class
      ]}
      data-index={@index}
      data-expanded={if @expanded, do: "true", else: "false"}
      phx-click={@on_click}
      {@rest}
    >
      <div class={["card-header", @header_class]}>
        {render_slot(@header)}
      </div>
      <div class={
        [
          "card-body",
          # Increased height to match post_body
          !@expanded && "max-h-40",
          @body_class
        ]
      }>
        {render_slot(@body)}
      </div>
      <div class={["card-footer", @footer_class]}>
        {render_slot(@footer)}
      </div>
    </div>
    """
  end
end
