defmodule HypeArrowWeb.PostController do
  use <PERSON>ype<PERSON><PERSON><PERSON>eb, :controller

  alias <PERSON>ypeArrow.Social
  alias HypeArrow.Social.Post

  action_fallback HypeArrowWeb.FallbackController

  def index(conn, _params) do
    posts = Social.list_posts()
    render(conn, :index, posts: posts)
  end

  def show(conn, %{"id" => id}) do
    with {:ok, id} <- parse_id(id),
         %Post{} = post <- Social.get_post(id) do
      render(conn, :show, post: post)
    else
      {:error, :invalid_id} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid ID format"})

      nil ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Post not found"})
    end
  end

  def create(conn, %{"post" => post_params}) do
    with {:ok, %Post{} = post} <- Social.create_post(post_params, conn.assigns.current_user) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", ~p"/api/posts/#{post}")
      |> render(:show, post: post)
    end
  end

  def update(conn, %{"id" => id, "post" => post_params}) do
    with {:ok, id} <- parse_id(id),
         %Post{} = post <- Social.get_post(id),
         {:ok, %Post{} = updated_post} <-
           Social.update_post(post, post_params, conn.assigns.current_user) do
      render(conn, :show, post: updated_post)
    else
      {:error, :invalid_id} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid ID format"})

      nil ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Post not found"})

      {:error, :unauthorized} ->
        conn
        |> put_status(:forbidden)
        |> json(%{error: "You are not authorized to update this post"})

      {:error, changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, changeset: changeset)
    end
  end

  def delete(conn, %{"id" => id}) do
    with {:ok, id} <- parse_id(id),
         %Post{} = post <- Social.get_post(id),
         {:ok, %Post{}} <- Social.delete_post(post, conn.assigns.current_user) do
      send_resp(conn, :no_content, "")
    else
      {:error, :invalid_id} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid ID format"})

      nil ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Post not found"})

      {:error, :unauthorized} ->
        conn
        |> put_status(:forbidden)
        |> json(%{error: "You are not authorized to delete this post"})
    end
  end

  # Helper function to parse and validate ID
  defp parse_id(id) do
    case Integer.parse(id) do
      {int_id, ""} when int_id > 0 -> {:ok, int_id}
      _ -> {:error, :invalid_id}
    end
  end
end
