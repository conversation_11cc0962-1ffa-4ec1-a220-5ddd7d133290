defmodule HypeArrow.Social.Post do
  use Ecto.Schema
  import Ecto.Changeset

  schema "posts" do
    field :title, :string
    field :content, :string
    field :likes_count, :integer, default: 0
    field :published_at, :utc_datetime

    belongs_to :user, HypeArrow.Accounts.User

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(post, attrs) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    post
    |> cast(attrs, [:title, :content, :likes_count, :published_at, :user_id])
    |> validate_required([:title, :content])
    |> validate_number(:likes_count, greater_than_or_equal_to: 0)
    |> put_change_if_empty(:published_at, now)
    |> foreign_key_constraint(:user_id)
  end

  defp put_change_if_empty(changeset, field, value) do
    if get_field(changeset, field) do
      changeset
    else
      put_change(changeset, field, value)
    end
  end
end
