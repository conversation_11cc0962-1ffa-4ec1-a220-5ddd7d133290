defmodule HypeArrowWeb.Live.Hooks do
  @moduledoc """
  Provides LiveView hooks for common functionality.
  
  This module centralizes LiveView hooks that can be used across different LiveViews
  to provide consistent behavior and reduce code duplication.
  """
  
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView
  
  alias HypeArrow.RealTime
  
  @doc """
  Hook to subscribe to real-time updates for posts.
  
  ## Examples
  
      # In a LiveView's mount callback:
      def mount(_params, _session, socket) do
        socket = HypeArrowWeb.Live.Hooks.subscribe_to_posts(socket)
        {:ok, socket}
      end
  """
  def subscribe_to_posts(socket) do
    if connected?(socket) do
      RealTime.subscribe(:posts)
    end
    
    socket
  end
  
  @doc """
  Hook to subscribe to real-time updates for a specific post.
  
  ## Examples
  
      # In a LiveView's mount callback:
      def mount(%{"id" => id}, _session, socket) do
        post_id = String.to_integer(id)
        socket = HypeArrowWeb.Live.Hooks.subscribe_to_post(socket, post_id)
        {:ok, socket}
      end
  """
  def subscribe_to_post(socket, post_id) when is_integer(post_id) do
    if connected?(socket) do
      RealTime.subscribe(:post, post_id)
    end
    
    socket
  end
  
  @doc """
  Hook to track page views.
  
  ## Examples
  
      # In a LiveView's mount callback:
      def mount(_params, _session, socket) do
        socket = HypeArrowWeb.Live.Hooks.track_page_view(socket, "posts_index")
        {:ok, socket}
      end
  """
  def track_page_view(socket, page_name) when is_binary(page_name) do
    if connected?(socket) do
      # Log page view (could be expanded to use analytics services)
      IO.puts("Page view: #{page_name} at #{DateTime.utc_now()}")
    end
    
    socket
  end
  
  @doc """
  Hook to initialize grid size from session or default.
  
  ## Examples
  
      # In a LiveView's mount callback:
      def mount(_params, session, socket) do
        socket = HypeArrowWeb.Live.Hooks.initialize_grid_size(socket, session)
        {:ok, socket}
      end
  """
  def initialize_grid_size(socket, session, default \\ 3) do
    # Get grid size from session or use default
    grid_size = 
      case session["grid_size"] do
        nil -> default
        size when is_binary(size) -> String.to_integer(size)
        size when is_integer(size) -> size
      end
    
    # Ensure grid size is within valid range
    grid_size = max(1, min(4, grid_size))
    
    assign(socket, :grid_size, grid_size)
  end
end
