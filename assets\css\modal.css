/*
 * Modal Styles
 * Custom styles for the modal component to match HypeArrow theme
 */

/* Modal background overlay */
[id$="-bg"] {
  background-color: rgba(10, 14, 23, 0.8) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Modal container */
.focus-wrap {
  background-color: rgba(15, 22, 35, 0.95) !important;
  border: 1px solid rgba(54, 199, 255, 0.2) !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(54, 199, 255, 0.1) !important;
  color: white !important;
  border-radius: 0.75rem !important;
  padding: 1.5rem !important;
}

/* Override default white background */
.focus-wrap div {
  background-color: transparent !important;
}

/* Modal title */
[id$="-content"] h2 {
  color: white !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin-bottom: 1.5rem !important;
  background: linear-gradient(135deg, #36C7FF 0%, #0E90FF 50%, #8A2BE2 100%);
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  text-align: center !important;
}

/* Close button */
.focus-wrap button[aria-label="close"] {
  color: white !important;
  opacity: 0.7 !important;
}

.focus-wrap button[aria-label="close"]:hover {
  opacity: 1 !important;
}

/* Form inputs */
.focus-wrap input[type="text"],
.focus-wrap textarea {
  background-color: rgba(26, 35, 51, 0.8) !important;
  border: 1px solid rgba(54, 199, 255, 0.2) !important;
  color: white !important;
  border-radius: 0.375rem !important;
}

.focus-wrap input[type="text"]:focus,
.focus-wrap textarea:focus {
  border-color: rgba(54, 199, 255, 0.5) !important;
  box-shadow: 0 0 0 2px rgba(54, 199, 255, 0.2) !important;
}

/* Form labels */
.focus-wrap label {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500 !important;
}

/* Error messages */
.focus-wrap .text-rose-600 {
  color: #ff4d7c !important;
}

/* Form buttons */
.focus-wrap .hype-btn-primary {
  background: linear-gradient(135deg, #36C7FF 0%, #0E90FF 50%, #8A2BE2 100%) !important;
  color: white !important;
  border: none !important;
  transition: opacity 0.2s ease-in-out !important;
}

.focus-wrap .hype-btn-primary:hover {
  opacity: 0.9 !important;
}

.focus-wrap .hype-btn-secondary {
  background-color: rgba(26, 35, 51, 0.8) !important;
  color: white !important;
  border: 1px solid rgba(54, 199, 255, 0.3) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.focus-wrap .hype-btn-secondary:hover {
  background-color: rgba(54, 199, 255, 0.2) !important;
}
