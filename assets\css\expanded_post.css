/*
 * Expanded Item Styles
 * These styles are specific to expanded items and not included in transparent_cards.css
 */

/* Add extra spacing when grid has an expanded item */
.content-grid.has-expanded-item,
.posts-grid.has-expanded-post {
  gap: 2.5rem; /* Increase gap between items when one is expanded */
  margin-bottom: 2rem; /* Add extra bottom margin */
  padding-bottom: 1rem; /* Add padding at the bottom */
}

/* Add extra spacing to items that come after an expanded item */
.content-grid.has-expanded-item .post-card:not(.expanded-post),
.posts-grid.has-expanded-post .post-card:not(.expanded-post) {
  position: relative; /* Ensure proper positioning */
  z-index: 1; /* Lower than expanded item */
  margin-top: 0.75rem; /* Add top margin to all non-expanded items */
  margin-bottom: 0.75rem; /* Add bottom margin to all non-expanded items */
}

/* Mobile-specific styles for expanded items */
@media (max-width: 767px) {
  /* Ensure expanded items have extra spacing on mobile */
  .expanded-post {
    padding-bottom: 1.5rem !important;
    transform: scale(1.1) !important; /* Slightly larger scale on mobile */
  }

  /* Add extra spacing to the grid when an item is expanded */
  .content-grid.has-expanded-item,
  .posts-grid.has-expanded-post {
    gap: 3rem !important;
    margin-bottom: 2.5rem !important;
  }
}

/* Grid layout styles moved to grid_controls.css */

/* Full-width row expansion styles */
.expanded-post {
  /* Enhanced visual appearance */
  border: 1px solid rgba(54, 199, 255, 0.5) !important;
  background-color: rgba(20, 30, 50, 0.95) !important; /* Slightly different background */

  /* Smooth transition */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  /* Ensure content is visible */
  max-height: none !important;
  overflow: visible !important;

  /* Increase height for better content visibility */
  min-height: 350px !important;

  /* Ensure it stands out */
  position: relative;
  z-index: 10;

  /* Add a more noticeable glow effect */
  box-shadow: 0 0 20px rgba(54, 199, 255, 0.5), 0 10px 25px rgba(0, 0, 0, 0.5) !important;

  /* Full width styles */
  width: 100% !important;
  margin: 1.5rem 0 !important;
  justify-self: stretch !important;

  /* Note: grid-column and grid-row are set dynamically by JavaScript */
  /* This ensures the post stays in its original row while spanning the full width */
}

/* Expanded post content */
.expanded-post .post-card-body {
  max-height: none !important;
  overflow: visible !important;
  word-wrap: break-word !important; /* Ensure long words don't overflow */
  overflow-wrap: break-word !important; /* Modern browsers */
  padding: 1rem !important; /* Add padding for better content display */
}

.expanded-post p {
  max-height: none !important;
  overflow: visible !important;
  display: block !important;
  -webkit-line-clamp: unset !important;
  word-wrap: break-word !important; /* Ensure long words don't overflow */
  overflow-wrap: break-word !important; /* Modern browsers */
  hyphens: auto !important; /* Add hyphens for very long words */
}

/* Fix for very long content */
.expanded-post .post-card-body:after {
  content: "";
  display: block;
  height: 1rem; /* Add extra space at the bottom */
  width: 100%;
}

/* Ensure expanded post content is properly displayed */
.expanded-post .post-card-body {
  overflow-y: auto !important;
  max-height: 600px !important; /* Increased max height for better content visibility */
  padding: 1.5rem !important; /* Add more padding for better readability */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth transition */
}

/* Ensure expanded post has appropriate dimensions */
.expanded-post {
  height: auto !important;
  min-height: 350px !important; /* Ensure minimum height for content */

  /* Animation for expanding */
  animation: expandPost 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animation for expanding posts */
@keyframes expandPost {
  from {
    opacity: 0.8;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Add a close button to expanded posts */
.expanded-post .post-collapse-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  z-index: 20;
  line-height: 1;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
  transition: opacity 0.2s ease;
  border: none;
  padding: 0;
}

.expanded-post .post-collapse-button:hover {
  opacity: 1;
}
