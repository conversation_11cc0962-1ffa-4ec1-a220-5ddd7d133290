defmodule HypeArrowWeb.ContentGridComponents do
  @moduledoc """
  Provides grid-related UI components for the application.

  These components handle grid layout controls and related functionality.
  """
  use Phoenix.Component

  # Import components
  import HypeArrowWeb.Components.ButtonComponents

  @doc """
  Renders grid size controls for adjusting the number of columns in a grid layout.

  ## Examples

      <.grid_size_controls grid_size={@grid_size} />
  """
  attr :grid_size, :integer, required: true
  attr :class, :string, default: nil

  def grid_size_controls(assigns) do
    ~H"""
    <div class={["grid-size-controls hidden md:flex items-center justify-end mb-4 mx-4", @class]}>
      <span class="text-sm text-gray-400 mr-3">Grid Size:</span>
      <div class="flex space-x-2">
        <.icon_only_button
          icon="hero-minus"
          variant={if @grid_size == 1, do: "primary", else: "ghost"}
          phx-click="change-grid-size"
          phx-value-size="1"
          class={"grid-size-btn #{if @grid_size == 1, do: "active", else: ""}"}
          aria-label="One item per row"
        />
        <.icon_only_button
          icon="hero-bars-2"
          variant={if @grid_size == 2, do: "primary", else: "ghost"}
          phx-click="change-grid-size"
          phx-value-size="2"
          class={"grid-size-btn #{if @grid_size == 2, do: "active", else: ""}"}
          aria-label="Two items per row"
        />
        <.icon_only_button
          icon="hero-bars-3"
          variant={if @grid_size == 3, do: "primary", else: "ghost"}
          phx-click="change-grid-size"
          phx-value-size="3"
          class={"grid-size-btn #{if @grid_size == 3, do: "active", else: ""}"}
          aria-label="Three items per row"
        />
        <.icon_only_button
          icon="hero-bars-4"
          variant={if @grid_size == 4, do: "primary", else: "ghost"}
          phx-click="change-grid-size"
          phx-value-size="4"
          class={"grid-size-btn #{if @grid_size == 4, do: "active", else: ""}"}
          aria-label="Four items per row"
        />
      </div>
    </div>
    """
  end

  @doc """
  Renders a generic content grid that can be used for any type of content.

  ## Examples

      <.content_grid
        id="posts-container"
        items={@posts}
        grid_size={@grid_size}
        expanded_item_id={@expanded_post_id}
        item_id_key={:id}
        phx_hook="PostsContainer"
      >
        <:item :let={{item, index, expanded}}>
          <div>Custom content for item {item.id}</div>
        </:item>
      </.content_grid>
  """
  attr :id, :string, default: "content-grid"
  attr :items, :list, required: true
  attr :grid_size, :integer, default: 3
  attr :expanded_item_id, :any, default: nil
  attr :item_id_key, :atom, default: :id
  attr :class, :string, default: nil
  attr :phx_hook, :string, default: nil

  slot :item, required: true

  def content_grid(assigns) do
    ~H"""
    <div id={@id} class={["content-grid", "grid-size-#{@grid_size}", @class]} phx-hook={@phx_hook}>
      <%= for {item, index} <- Enum.with_index(@items) do %>
        <% item_id = Map.get(item, @item_id_key) %>
        <% expanded = @expanded_item_id == item_id %>
        {render_slot(@item, {item, index, expanded})}
      <% end %>
    </div>
    """
  end
end
