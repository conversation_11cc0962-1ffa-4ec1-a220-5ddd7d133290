defmodule HypeArrowWeb.Components.ButtonComponents do
  @moduledoc """
  Provides button UI components for the application.
  
  These components handle rendering of various button styles with consistent appearance.
  """
  use Phoenix.Component
  
  # Import components
  import HypeArrowWeb.CoreComponents
  
  @doc """
  Renders a button with an optional icon.
  
  ## Examples
  
      <.icon_button icon="hero-heart">Like</.icon_button>
      <.icon_button icon="hero-chat-bubble-left" variant="secondary">Comment</.icon_button>
  """
  attr :icon, :string, default: nil
  attr :variant, :string, default: "primary", values: ~w(primary secondary outline ghost)
  attr :class, :string, default: nil
  attr :rest, :global, include: ~w(disabled form name value type phx-click)
  slot :inner_block, required: true
  
  def icon_button(assigns) do
    ~H"""
    <button
      class={[
        "inline-flex items-center justify-center rounded-md font-medium transition-colors",
        # Variant classes
        @variant == "primary" && "bg-brand hover:bg-brand-light text-white",
        @variant == "secondary" && "bg-gray-700 hover:bg-gray-600 text-white",
        @variant == "outline" && "border border-gray-600 hover:bg-gray-700 text-gray-300",
        @variant == "ghost" && "hover:bg-gray-700 text-gray-300",
        @class
      ]}
      {@rest}
    >
      <%= if @icon do %>
        <.icon name={@icon} class="h-5 w-5 mr-1.5" />
      <% end %>
      {render_slot(@inner_block)}
    </button>
    """
  end
  
  @doc """
  Renders a circular icon button.
  
  ## Examples
  
      <.icon_only_button icon="hero-x-mark" />
      <.icon_only_button icon="hero-heart" variant="secondary" />
  """
  attr :icon, :string, required: true
  attr :variant, :string, default: "ghost", values: ~w(primary secondary outline ghost)
  attr :size, :string, default: "md", values: ~w(sm md lg)
  attr :class, :string, default: nil
  attr :rest, :global, include: ~w(disabled form name value type phx-click)
  
  def icon_only_button(assigns) do
    ~H"""
    <button
      class={[
        "inline-flex items-center justify-center rounded-full transition-colors",
        # Size classes
        @size == "sm" && "p-1",
        @size == "md" && "p-2",
        @size == "lg" && "p-3",
        # Variant classes
        @variant == "primary" && "bg-brand hover:bg-brand-light text-white",
        @variant == "secondary" && "bg-gray-700 hover:bg-gray-600 text-white",
        @variant == "outline" && "border border-gray-600 hover:bg-gray-700 text-gray-300",
        @variant == "ghost" && "hover:bg-gray-700 text-gray-300",
        @class
      ]}
      {@rest}
    >
      <%= cond do %>
        <% @size == "sm" -> %>
          <.icon name={@icon} class="h-4 w-4" />
        <% @size == "md" -> %>
          <.icon name={@icon} class="h-5 w-5" />
        <% @size == "lg" -> %>
          <.icon name={@icon} class="h-6 w-6" />
      <% end %>
    </button>
    """
  end
end
