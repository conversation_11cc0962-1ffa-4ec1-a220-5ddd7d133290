defmodule HypeArrowWeb.Live.Helpers do
  @moduledoc """
  Provides helper functions for LiveViews.

  This module contains common helper functions that can be used across different
  LiveViews to reduce code duplication and improve consistency.
  """

  import Phoenix.Component, only: [assign: 3]

  @doc """
  Assigns a value to the socket if it doesn't already exist.

  ## Examples

      socket = assign_new_if_missing(socket, :posts, fn -> fetch_posts() end)
  """
  def assign_new_if_missing(socket, key, fun) when is_atom(key) and is_function(fun, 0) do
    if Map.has_key?(socket.assigns, key) do
      socket
    else
      assign(socket, key, fun.())
    end
  end

  @doc """
  Assigns a default value to the socket if the key doesn't exist or is nil.

  ## Examples

      socket = assign_default(socket, :grid_size, 3)
  """
  def assign_default(socket, key, default) when is_atom(key) do
    if is_nil(socket.assigns[key]) do
      assign(socket, key, default)
    else
      socket
    end
  end

  @doc """
  Safely parses an integer from a string, returning a default value if parsing fails.

  ## Examples

      id = safe_parse_integer(params["id"], nil)
  """
  def safe_parse_integer(value, default \\ nil)

  def safe_parse_integer(value, default) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> default
    end
  end

  def safe_parse_integer(value, _default) when is_integer(value), do: value
  def safe_parse_integer(_, default), do: default

  @doc """
  Safely converts a value to an integer, returning a default value if conversion fails.

  ## Examples

      page = to_integer(params["page"], 1)
  """
  def to_integer(value, default \\ nil)

  def to_integer(value, _default) when is_integer(value), do: value

  def to_integer(value, default) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> default
    end
  end

  def to_integer(_, default), do: default

  @doc """
  Handles pagination parameters from the socket assigns.

  ## Examples

      {page, per_page} = handle_pagination_params(socket)
  """
  def handle_pagination_params(socket, default_page \\ 1, default_per_page \\ 10) do
    page = socket.assigns[:page] || default_page
    per_page = socket.assigns[:per_page] || default_per_page

    {page, per_page}
  end

  @doc """
  Builds pagination assigns for a LiveView.

  ## Examples

      socket =
        socket
        |> assign(:page, 1)
        |> build_pagination_assigns(total_count, per_page)
  """
  def build_pagination_assigns(socket, total_count, per_page \\ 10) do
    page = socket.assigns[:page] || 1

    total_pages = max(1, ceil(total_count / per_page))
    has_next = page < total_pages
    has_prev = page > 1

    socket
    |> assign(:total_pages, total_pages)
    |> assign(:has_next_page, has_next)
    |> assign(:has_prev_page, has_prev)
    |> assign(:total_count, total_count)
  end

  @doc """
  Handles a LiveView event with debounce.

  ## Examples

      def handle_event("search", %{"query" => query}, socket) do
        handle_debounced_event(socket, :search_timer, 300, fn ->
          perform_search(query)
        end)
      end
  """
  def handle_debounced_event(socket, timer_ref, debounce_ms, callback)
      when is_atom(timer_ref) and is_function(callback, 0) do
    # Cancel any existing timer
    if timer_ref = socket.assigns[timer_ref] do
      Process.cancel_timer(timer_ref)
    end

    # Set a new timer
    timer_ref = Process.send_after(self(), {:debounced_event, callback}, debounce_ms)

    # Return the socket with the new timer reference
    {:noreply, assign(socket, timer_ref, timer_ref)}
  end

  @doc """
  Handles the debounced event callback.

  This should be called from a handle_info callback in your LiveView.

  ## Examples

      def handle_info({:debounced_event, callback}, socket) do
        handle_debounced_callback(socket, callback)
      end
  """
  def handle_debounced_callback(_socket, callback) when is_function(callback, 0) do
    # Execute the callback
    result = callback.()

    # Return the result
    case result do
      {:ok, updated_socket} -> {:noreply, updated_socket}
      {:error, updated_socket} -> {:noreply, updated_socket}
      updated_socket -> {:noreply, updated_socket}
    end
  end
end
