/*
 * Grid Size Controls
 * Styles for the grid size control buttons and grid layouts
 */

/* Grid size control buttons */
.grid-size-controls {
  background-color: rgba(15, 22, 35, 0.7);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 4px 15px -3px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(54, 199, 255, 0.1);
  border: 1px solid rgba(26, 35, 51, 0.8);
}

.grid-size-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.375rem;
  background-color: rgba(26, 35, 51, 0.6);
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.2s ease-in-out;
}

.grid-size-btn:hover {
  background-color: rgba(54, 199, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.grid-size-btn.active {
  background-color: rgba(54, 199, 255, 0.4);
  color: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 2px rgba(54, 199, 255, 0.6);
  transform: translateY(-1px);
}

/* Grid layouts */
/* Default (mobile): 1 column */
.content-grid, .posts-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
  grid-auto-flow: row;
  grid-auto-rows: auto;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* When a post is expanded, increase the gap to provide more space */
.content-grid.has-expanded-item, .posts-grid.has-expanded-post {
  gap: 2.5rem;
}

/* Medium screens (tablet) */
@media (min-width: 768px) {
  /* Grid size 1: Force 1 column */
  .content-grid.grid-size-1, .posts-grid.grid-size-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  /* Grid size 2: 2 columns */
  .content-grid.grid-size-2, .posts-grid.grid-size-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Grid size 3: 2 columns on medium screens */
  .content-grid.grid-size-3, .posts-grid.grid-size-3 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Grid size 4: 2 columns on medium screens */
  .content-grid.grid-size-4, .posts-grid.grid-size-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Large screens (desktop) */
@media (min-width: 1024px) {
  /* Grid size 1: Force 1 column */
  .content-grid.grid-size-1, .posts-grid.grid-size-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  /* Grid size 2: 2 columns */
  .content-grid.grid-size-2, .posts-grid.grid-size-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Grid size 3: 3 columns */
  .content-grid.grid-size-3, .posts-grid.grid-size-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  /* Grid size 4: 3 columns on large screens */
  .content-grid.grid-size-4, .posts-grid.grid-size-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Extra large screens */
@media (min-width: 1280px) {
  /* Grid size 4: 4 columns on extra large screens */
  .content-grid.grid-size-4, .posts-grid.grid-size-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Expanded items styling */
.content-grid.has-expanded-item .expanded-post,
.posts-grid.has-expanded-post .expanded-post {
  /* Position and z-index */
  position: relative;
  z-index: 10;

  /* Ensure the expanded post takes up the full width */
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  justify-self: stretch !important;

  /* Add animation for smooth transition */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  /* Note: grid-column and grid-row are set dynamically by JavaScript */
  /* This ensures the post stays in its original row while spanning the full width */
}
