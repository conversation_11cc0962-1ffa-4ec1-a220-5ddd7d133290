defmodule HypeArrowWeb.Components.EditPostModal do
  use Phoenix.Component
  use HypeArrowWeb, :verified_routes

  import HypeArrowWeb.CoreComponents
  alias Phoenix.LiveView.JS

  @doc """
  Renders a modal for editing a post.
  """
  attr :show, :boolean, default: false
  attr :post, :map, default: nil
  attr :on_save, :any, default: nil

  def edit_post_modal(assigns) do
    ~H"""
    <.modal id="edit-post-modal" show={@show} on_cancel={JS.push("close-edit-modal")}>
      <.header>Edit Post</.header>

      <.simple_form for={%{}} id="edit-post-form" phx-submit={@on_save}>
        <.input type="text" name="post[title]" value={@post && @post.title} label="Title" required />
        <.input
          type="textarea"
          name="post[content]"
          value={@post && @post.content}
          label="Content"
          required
          rows={5}
        />
        <input type="hidden" name="post_id" value={@post && @post.id} />

        <:actions>
          <.button type="button" phx-click={JS.push("close-edit-modal")} class="mr-2">Cancel</.button>
          <.button type="submit" phx-disable-with="Saving...">Save Changes</.button>
        </:actions>
      </.simple_form>
    </.modal>
    """
  end
end
