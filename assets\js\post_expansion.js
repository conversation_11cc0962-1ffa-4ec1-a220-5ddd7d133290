// Post expansion functionality - FULL-WIDTH ROW EXPANSION WITH RELIABLE SCROLLING
const setupPostExpansion = () => {
  // Store state
  let expandedPostId = null;
  let expandedPost = null;
  let scrollPosition = 0;
  let postPositions = new Map(); // Store positions of posts

  // Function to set up the expanded post layout
  // This ensures the post spans the full width and the grid layout flows naturally
  const setCorrectRowPositions = (expandedPost, grid) => {
    console.log('Setting up expanded post layout for:', expandedPost.id);

    // Get all posts in the grid
    const allPosts = Array.from(grid.querySelectorAll('.post-card'));
    if (allPosts.length === 0) {
      console.error('No posts found in the grid');
      return;
    }

    console.log(`Found ${allPosts.length} posts in the grid`);

    // Clear any existing grid positioning on all posts to reset the layout
    allPosts.forEach(post => {
      if (post !== expandedPost) {
        post.style.removeProperty('grid-row');
        post.style.removeProperty('grid-column');
      }
    });

    // Set the expanded post to span the full width
    // This is the key change - let CSS Grid handle the natural flow
    expandedPost.style.setProperty('grid-column', '1 / -1', 'important');

    // Remove any explicit grid-row positioning to let CSS Grid handle the natural flow
    // The CSS rules in grid_controls.css and expanded_post.css will handle the layout
    expandedPost.style.removeProperty('grid-row');

    // Force a reflow to ensure the grid layout is updated
    grid.offsetHeight;

    console.log('Finished setting up expanded post layout - letting CSS Grid handle the flow');
  };

  // Function to handle post expansion
  window.handlePostExpansion = (postId) => {
    console.log('Handling post expansion for:', postId);

    // Get the post element
    const post = document.getElementById(`post-${postId}`);
    if (!post) {
      console.error('Post not found:', postId);
      return;
    }

    // If this post is already expanded, collapse it
    if (expandedPostId === postId) {
      handlePostCollapse();
      return;
    }

    // If another post is expanded, collapse it first but don't scroll back
    if (expandedPostId) {
      // Save the fact that we're switching posts
      const isSwitchingPosts = true;

      // Collapse without scrolling back
      handlePostCollapse(isSwitchingPosts);
    }

    // CRITICAL: Get the correct scrollable container
    const scrollContainer = document.querySelector('.hype-main-content');
    if (!scrollContainer) {
      console.error('Scrollable container not found');
      return;
    }

    // Save the current scroll position of the container
    scrollPosition = scrollContainer.scrollTop;
    console.log('Saved container scroll position:', scrollPosition);

    // Save the post's position relative to the container
    const rect = post.getBoundingClientRect();

    // Store the container and position information
    postPositions.set(postId, {
      container: scrollContainer,
      scrollY: scrollPosition
    });

    console.log(`Saved position for post ${postId}:`, {
      scrollY: scrollPosition,
      postTop: rect.top
    });

    // Mark this post as expanded
    expandedPostId = postId;
    expandedPost = post;

    // Update the URL and notify Phoenix LiveView about the expanded post
    try {
      // Only update if the URL doesn't already contain this post ID
      const currentPath = window.location.pathname;
      const expectedPath = `/posts/${postId}`;

      if (currentPath !== expectedPath) {
        console.log(`Updating URL to ${expectedPath}`);

        // Try to notify Phoenix LiveView first
        const postsContainer = document.querySelector('[phx-hook="PostsContainer"]');
        if (postsContainer && window.liveSocket) {
          // Get the LiveView instance
          const view = window.liveSocket.getViewByEl(postsContainer);
          if (view) {
            // Push the toggle-post event to Phoenix
            console.log('Notifying Phoenix LiveView about post expansion');
            view.pushEvent('toggle-post', { id: parseInt(postId) });
          } else {
            // Fallback: update the URL directly
            window.history.pushState({}, '', expectedPath);
          }
        } else {
          // Fallback: update the URL directly
          window.history.pushState({}, '', expectedPath);
        }
      }
    } catch (e) {
      console.error('Error updating URL:', e);
    }

    // Add expanded class to the post
    post.classList.add('expanded-post');

    // Add the has-expanded-post class to the grid container
    // Look for both .posts-grid and .content-grid to handle different container types
    const postsGrid = post.closest('.posts-grid') || post.closest('.content-grid');
    if (postsGrid) {
      // Add the appropriate class based on the container type
      if (postsGrid.classList.contains('content-grid')) {
        postsGrid.classList.add('has-expanded-item');
      } else {
        postsGrid.classList.add('has-expanded-post');
      }

      // First, ensure the post is visible as expanded
      post.style.setProperty('grid-column', '1 / -1', 'important');

      // Force a reflow to ensure the expanded class is applied
      post.offsetHeight;

      // Calculate and set the correct row for the expanded post
      // and adjust other posts in the same row
      setCorrectRowPositions(post, postsGrid);

      // Run it again after a delay to ensure it works with all DOM updates
      setTimeout(() => {
        setCorrectRowPositions(post, postsGrid);
      }, 50);

      // And one more time after animations should be complete
      setTimeout(() => {
        setCorrectRowPositions(post, postsGrid);
      }, 400);
    }

    // Check if the post already has a Phoenix-generated close button
    const existingCloseButton = post.querySelector('.post-card-header button');

    // If there's no Phoenix-generated close button, add our JavaScript one
    if (!existingCloseButton) {
      // First, remove any existing JS-added close buttons to avoid duplicates
      post.querySelectorAll('.post-collapse-button').forEach(btn => btn.remove());

      // Now add a single close button
      const closeButton = document.createElement('button');
      closeButton.className = 'post-collapse-button';
      closeButton.innerHTML = '×';
      closeButton.setAttribute('aria-label', 'Close');
      closeButton.addEventListener('click', (e) => {
        e.stopPropagation();
        handlePostCollapse();
      });
      post.appendChild(closeButton);
    } else {
      // Don't add our own event listener to the Phoenix-generated button
      // The Phoenix LiveView will handle the click event through phx-click
      // Adding our own event listener would interfere with Phoenix's event handling
    }

    // Scroll to the post using the container
    // Use a slightly longer delay when switching posts to ensure the DOM has updated
    const scrollDelay = expandedPostId ? 100 : 50;

    setTimeout(() => {
      try {
        const headerHeight = document.querySelector('.hype-navbar')?.offsetHeight || 0;
        const offset = headerHeight + 20;

        // Get the post's position relative to the container
        const postRect = post.getBoundingClientRect();
        const containerRect = scrollContainer.getBoundingClientRect();
        const relativeTop = postRect.top - containerRect.top + scrollContainer.scrollTop;

        // For full-width row expansion, we want to align the post to the top of the viewport
        // with a small offset for better visibility
        const headerOffset = 20; // Additional offset beyond the header height

        // Scroll the container to align the post to the top
        scrollContainer.scrollTo({
          top: Math.max(0, relativeTop - offset - headerOffset),
          behavior: 'smooth'
        });

        // Add multiple backup scroll attempts for reliability
        // This helps ensure the scroll position is maintained even after the post expands
        setTimeout(() => {
          scrollContainer.scrollTo({
            top: Math.max(0, relativeTop - offset - headerOffset),
            behavior: 'smooth'
          });
        }, 150);

        // Add another attempt after the transition should be complete
        setTimeout(() => {
          scrollContainer.scrollTo({
            top: Math.max(0, relativeTop - offset - headerOffset),
            behavior: 'auto'
          });
        }, 450);

        console.log('Scrolled container to post at position:', relativeTop - offset);
      } catch (e) {
        console.error('Error scrolling to post:', e);
      }
    }, scrollDelay);
  };

  // Function to handle post collapse - PRIVATE to this module
  // isSwitchingPosts: if true, don't scroll back (we're about to show another post)
  const handlePostCollapse = (isSwitchingPosts = false) => {
    // If we have a specific post to collapse
    if (expandedPostId && expandedPost) {
      // Remove expanded class from the specific post
      expandedPost.classList.remove('expanded-post');
    } else {
      // If no specific post is tracked, collapse all expanded posts
      document.querySelectorAll('.expanded-post').forEach(post => {
        post.classList.remove('expanded-post');
      });

      // Exit early since we don't have a specific post to work with
      if (!expandedPostId && !expandedPost) return;
    }

    // As a safety measure, ensure no other posts have the expanded class
    document.querySelectorAll('.expanded-post').forEach(post => {
      if (!expandedPost || post.id !== expandedPost.id) {
        post.classList.remove('expanded-post');
      }
    });

    // Remove the has-expanded-post/has-expanded-item class from the grid container
    // Look for both .posts-grid and .content-grid to handle different container types
    document.querySelectorAll('.posts-grid.has-expanded-post, .content-grid.has-expanded-item').forEach(grid => {
      // Remove the appropriate class based on the container type
      if (grid.classList.contains('content-grid')) {
        grid.classList.remove('has-expanded-item');
      } else {
        grid.classList.remove('has-expanded-post');
      }

      // Reset grid-row styles for all posts
      grid.querySelectorAll('.post-card').forEach(post => {
        post.style.removeProperty('grid-row');
        post.style.removeProperty('grid-column');
      });

      console.log('Reset grid-row and grid-column styles for all posts');
    });

    // Remove any JavaScript-added close button
    const closeButton = expandedPost.querySelector('.post-collapse-button');
    if (closeButton) {
      closeButton.remove();
    }

    // Remove event listeners from Phoenix-generated close button
    const phoenixCloseButton = expandedPost.querySelector('.post-card-header button');
    if (phoenixCloseButton) {
      // Clone and replace to remove event listeners
      const newButton = phoenixCloseButton.cloneNode(true);
      phoenixCloseButton.parentNode.replaceChild(newButton, phoenixCloseButton);
    }

    // Get the saved position
    const position = postPositions.get(expandedPostId);

    // Clear state BEFORE scrolling
    const postIdToRestore = expandedPostId;

    // Notify Phoenix LiveView that we're collapsing this post
    // This is critical to keep the LiveView state in sync with our JavaScript state
    try {
      // Get the post ID from the element ID (format: "post-123")
      const postId = expandedPost.id.split('-')[1];
      if (postId) {
        console.log(`Notifying Phoenix LiveView to collapse post ${postId}`);

        // Try multiple approaches to update the Phoenix LiveView state

        // Use the LiveView hook to push an event directly
        // This is more reliable than trying to click the Phoenix button
        const postsContainer = document.querySelector('[phx-hook="PostsContainer"]');
        if (postsContainer && window.liveSocket) {
          console.log('Using LiveView hook to collapse post');
          // Get the LiveView instance
          const view = window.liveSocket.getViewByEl(postsContainer);
          if (view) {
            // Push the toggle-post event
            view.pushEvent('toggle-post', { id: parseInt(postId) });
            return; // Exit early as the Phoenix event will handle everything
          }
        }

        // Approach 3: Update the URL directly as a last resort
        console.log('Falling back to URL update to collapse post');
        window.history.pushState({}, '', '/');
      }
    } catch (e) {
      console.error('Error notifying Phoenix LiveView:', e);
    }

    // Clear our local state
    expandedPostId = null;
    expandedPost = null;

    // If we have a saved position and we're not switching posts, restore it
    if (position && position.container && !isSwitchingPosts) {
      console.log(`Restoring position for post ${postIdToRestore}:`, position);

      // Get the container and scroll position
      const container = position.container;
      const scrollY = position.scrollY;

      // Scroll the container to the saved position
      try {
        // Immediate scroll
        container.scrollTop = scrollY;

        // Multiple backup attempts with setTimeout
        setTimeout(() => {
          container.scrollTop = scrollY;

          // Try again after a bit longer
          setTimeout(() => {
            container.scrollTop = scrollY;
          }, 50);

          // And again after even longer
          setTimeout(() => {
            container.scrollTop = scrollY;
          }, 150);
        }, 10);

        console.log('Restored container scroll position to:', scrollY);
      } catch (e) {
        console.error('Error restoring scroll position:', e);
      }
    } else if (isSwitchingPosts) {
      console.log('Switching posts - not scrolling back to previous position');
    }
  };

  // Function to handle post expansion for editing
  // This first expands the post using the existing expansion logic, then enables edit mode
  window.handlePostExpansionForEdit = (postId) => {
    console.log('=== EDIT EXPANSION START ===');
    console.log('Handling post expansion for editing:', postId);

    // First, expand the post using the existing expansion functionality
    // This ensures we get the proper full-width row layout
    console.log('Step 1: Calling handlePostExpansion for post:', postId);
    handlePostExpansion(postId);

    // After the expansion is complete, notify Phoenix to enable edit mode
    // Use a delay to ensure the expansion animation is complete
    console.log('Step 2: Setting timeout to enable edit mode after expansion');
    setTimeout(() => {
      console.log('Step 3: Timeout triggered, attempting to enable edit mode');
      const postsContainer = document.querySelector('[phx-hook="PostsContainer"]');
      if (postsContainer && window.liveSocket) {
        console.log('Step 4: Found posts container and liveSocket, getting view');
        // Get the LiveView instance
        const view = window.liveSocket.getViewByEl(postsContainer);
        if (view) {
          console.log('Step 5: Found LiveView, pushing enable-edit-mode event');
          // Push the enable-edit-mode event after expansion
          view.pushEvent('enable-edit-mode', { id: parseInt(postId) });
          console.log('=== EDIT EXPANSION COMPLETE ===');
        } else {
          console.error('Step 5 ERROR: Could not get LiveView instance');
        }
      } else {
        console.error('Step 4 ERROR: Could not find posts container or liveSocket');
        console.log('postsContainer:', postsContainer);
        console.log('window.liveSocket:', window.liveSocket);
      }
    }, 450); // Wait for expansion animation to complete
  };

  // Expose the collapse function globally
  window.handlePostCollapse = (isSwitchingPosts = false) => handlePostCollapse(isSwitchingPosts);

  // Add a global function to collapse all posts (useful for debugging)
  window.collapseAllPosts = () => {
    console.log('Collapsing all posts');

    // First check if there's a currently tracked expanded post
    if (expandedPostId && expandedPost) {
      // Use the regular collapse function to ensure Phoenix is notified
      handlePostCollapse();
      return;
    }

    // If no post is tracked but there are expanded posts, find the first one and collapse it
    const expandedPosts = document.querySelectorAll('.expanded-post');
    if (expandedPosts.length > 0) {
      // Get the first expanded post
      const firstExpandedPost = expandedPosts[0];

      // Get the post ID from the element ID (format: "post-123")
      const postId = firstExpandedPost.id.split('-')[1];
      if (postId) {
        // Use the LiveView hook to push an event directly
        const postsContainer = document.querySelector('[phx-hook="PostsContainer"]');
        if (postsContainer && window.liveSocket) {
          console.log('Using LiveView hook to collapse post');
          // Get the LiveView instance
          const view = window.liveSocket.getViewByEl(postsContainer);
          if (view) {
            // Push the toggle-post event
            view.pushEvent('toggle-post', { id: parseInt(postId) });
            return; // Exit early as the Phoenix event will handle everything
          }
        }
      }

      // If no Phoenix button, update the URL directly
      console.log('Falling back to URL update to collapse all posts');
      window.history.pushState({}, '', '/');
    }

    // As a last resort, just remove the class from all posts
    document.querySelectorAll('.expanded-post').forEach(post => {
      post.classList.remove('expanded-post');
    });

    // Clear state
    expandedPostId = null;
    expandedPost = null;
  };

  // Handle ESC key to collapse
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && expandedPostId) {
      // When using ESC key, we want to make sure Phoenix is notified
      // Use the LiveView hook to push an event directly
      if (expandedPostId) {
        const postsContainer = document.querySelector('[phx-hook="PostsContainer"]');
        if (postsContainer && window.liveSocket) {
          console.log('Using LiveView hook for ESC key collapse');
          // Get the LiveView instance
          const view = window.liveSocket.getViewByEl(postsContainer);
          if (view) {
            // Push the toggle-post event
            view.pushEvent('toggle-post', { id: parseInt(expandedPostId) });
            return; // Exit early as the Phoenix event will handle everything
          }
        }
      }

      // Fall back to our regular collapse function
      handlePostCollapse();
    }
  });

  // Handle clicks on the close button inside expanded posts
  document.addEventListener('click', (e) => {
    if (e.target.closest('.post-collapse-button') && expandedPostId) {
      e.preventDefault();
      e.stopPropagation();

      // When clicking our JavaScript close button, we want to make sure Phoenix is notified
      // Use the LiveView hook to push an event directly
      if (expandedPostId) {
        const postsContainer = document.querySelector('[phx-hook="PostsContainer"]');
        if (postsContainer && window.liveSocket) {
          console.log('Using LiveView hook for JS button collapse');
          // Get the LiveView instance
          const view = window.liveSocket.getViewByEl(postsContainer);
          if (view) {
            // Push the toggle-post event
            view.pushEvent('toggle-post', { id: parseInt(expandedPostId) });
            return; // Exit early as the Phoenix event will handle everything
          }
        }
      }

      // Fall back to our regular collapse function
      handlePostCollapse();
    }
  });

  // Handle maintain scroll position event from LiveView
  window.addEventListener('phx:maintain-scroll-position', (e) => {
    const { post_id } = e.detail;
    console.log('Maintaining scroll position for post:', post_id);

    // Get the post element
    const post = document.getElementById(`post-${post_id}`);
    if (!post) {
      console.error('Post not found:', post_id);
      return;
    }

    // Get the scrollable container
    const scrollContainer = document.querySelector('.hype-main-content');
    if (!scrollContainer) {
      console.error('Scrollable container not found');
      return;
    }

    // Get the grid container
    // Look for both .posts-grid and .content-grid to handle different container types
    const postsGrid = post.closest('.posts-grid') || post.closest('.content-grid');
    if (postsGrid) {
      // Add the appropriate class based on the container type
      if (postsGrid.classList.contains('content-grid')) {
        postsGrid.classList.add('has-expanded-item');
      } else {
        postsGrid.classList.add('has-expanded-post');
      }

      // First, ensure the post is visible as expanded
      post.style.setProperty('grid-column', '1 / -1', 'important');

      // Force a reflow to ensure the expanded class is applied
      post.offsetHeight;

      // Calculate and set the correct row for the expanded post
      setCorrectRowPositions(post, postsGrid);

      // Run it again after a delay to ensure it works
      setTimeout(() => {
        setCorrectRowPositions(post, postsGrid);
      }, 100);
    }

    // Calculate the position to scroll to
    const headerHeight = document.querySelector('.hype-navbar')?.offsetHeight || 0;
    const offset = headerHeight + 20;

    // Get the post's position relative to the container
    const postRect = post.getBoundingClientRect();
    const containerRect = scrollContainer.getBoundingClientRect();
    const relativeTop = postRect.top - containerRect.top + scrollContainer.scrollTop;

    // For full-width row expansion, we want to align the post to the top of the viewport
    // with a small offset for better visibility
    const headerOffset = 20; // Additional offset beyond the header height

    // Scroll the container to align the post to the top
    scrollContainer.scrollTo({
      top: Math.max(0, relativeTop - offset - headerOffset),
      behavior: 'smooth'
    });

    // Add multiple backup scroll attempts for reliability
    // This helps ensure the scroll position is maintained even after the post expands
    setTimeout(() => {
      scrollContainer.scrollTo({
        top: Math.max(0, relativeTop - offset - headerOffset),
        behavior: 'smooth'
      });
    }, 100);

    // Add another attempt after the transition should be complete
    setTimeout(() => {
      scrollContainer.scrollTo({
        top: Math.max(0, relativeTop - offset - headerOffset),
        behavior: 'auto'
      });
    }, 450);
  });
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // First, check if there are any expanded posts from Phoenix
  // If there's exactly one, we'll let it be
  // If there are multiple, collapse all but the first one
  const expandedPosts = document.querySelectorAll('.expanded-post');
  if (expandedPosts.length > 1) {
    console.warn(`Found ${expandedPosts.length} expanded posts at initialization, fixing...`);

    // Keep only the first one expanded
    for (let i = 1; i < expandedPosts.length; i++) {
      expandedPosts[i].classList.remove('expanded-post');
    }
  }

  // If there's an expanded post, make sure it's positioned correctly
  if (expandedPosts.length > 0) {
    const expandedPost = expandedPosts[0];
    // Look for both .posts-grid and .content-grid to handle different container types
    const postsGrid = expandedPost.closest('.posts-grid') || expandedPost.closest('.content-grid');

    if (postsGrid) {
      // Add the appropriate class based on the container type
      if (postsGrid.classList.contains('content-grid')) {
        postsGrid.classList.add('has-expanded-item');
      } else {
        postsGrid.classList.add('has-expanded-post');
      }

      // Use a delay to ensure the DOM is fully loaded
      setTimeout(() => {
        // Calculate and set the correct row positions
        setCorrectRowPositions(expandedPost, postsGrid);

        // Run it again after a longer delay to ensure it works
        setTimeout(() => {
          setCorrectRowPositions(expandedPost, postsGrid);
        }, 100);
      }, 50);
    }
  }

  // Now initialize our expansion functionality
  setupPostExpansion();
});

export default setupPostExpansion;








