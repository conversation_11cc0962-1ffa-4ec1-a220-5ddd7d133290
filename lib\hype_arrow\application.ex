defmodule HypeArrow.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      HypeArrowWeb.Telemetry,
      HypeArrow.Repo,
      {DNSCluster, query: Application.get_env(:hype_arrow, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: HypeArrow.PubSub},
      # Start the Finch HTTP client for sending emails
      {<PERSON>, name: <PERSON>ype<PERSON><PERSON>.Finch},
      # Start a worker by calling: HypeArrow.Worker.start_link(arg)
      # {HypeArrow.Worker, arg},
      # Start to serve requests, typically the last entry
      HypeArrowWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: HypeArrow.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    HypeArrowWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
