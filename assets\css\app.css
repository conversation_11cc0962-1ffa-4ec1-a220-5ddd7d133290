@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* This file is for your main application CSS */
@import "./expanded_post.css";
@import "./transparent_cards.css";
@import "./infinite_scroll.css";
@import "./grid_controls.css";
@import "./modal.css";

@layer components {
  /* Layout Components */
  .hype-container {
    @apply max-w-7xl mx-auto px-4 pb-12 sm:px-6 lg:px-8;
  }

  .hype-navbar {
    @apply bg-background-dark border-b border-background-light sticky top-0 z-50;
  }

  .hype-navbar-content {
    @apply flex items-center justify-between h-16;
  }

  .hype-sidebar {
    @apply bg-background w-64 fixed h-full overflow-y-auto border-r border-background-light z-30 transition-transform duration-300 ease-in-out transform top-0 left-0 pt-16;
  }

  .hype-main-content {
    @apply pt-6 pb-12 min-h-screen bg-background-dark overflow-y-auto transition-all duration-300 ease-in-out;
    scroll-behavior: smooth;
    background-image: linear-gradient(135deg, #0A0E17 0%, #0F1623 50%, #1A2333 100%);
  }

  /* Mobile sidebar styles */
  @media (max-width: 768px) {
    .hype-sidebar {
      @apply -translate-x-full shadow-lg;
    }

    .hype-sidebar.open {
      @apply translate-x-0 !block;
    }

    /* Ensure the sidebar is above other content */
    #sidebar-overlay {
      @apply z-20 fixed inset-0 bg-black bg-opacity-50;
    }
  }

  /* Navigation Components */
  .hype-nav-link {
    @apply flex items-center px-4 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-background-light hover:text-white transition-colors;
  }

  .hype-nav-link-active {
    @apply bg-background-light text-white;
  }

  /* Button Components */
  .hype-btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand transition-colors;
  }

  .hype-btn-primary {
    @apply bg-gradient-brand text-white hover:opacity-90;
  }

  .hype-btn-secondary {
    @apply bg-background-light text-white hover:bg-background border border-brand;
  }

  /* Card Components */
  /* Card styles moved to transparent_cards.css */

  /* Post Components */
  /* Post styles moved to components */

  /* Form Components */
  .hype-input {
    @apply block w-full rounded-md border-background-light bg-background-dark text-white shadow-sm focus:border-brand focus:ring-brand;
  }

  .hype-label {
    @apply block text-sm font-medium text-gray-300 mb-1;
  }

  /* Utility Components */
  .hype-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .hype-badge-blue {
    @apply bg-brand/20 text-brand-light;
  }

  .hype-badge-purple {
    @apply bg-brand-purple/20 text-brand-purple;
  }

  .hype-divider {
    @apply border-t border-background-light my-4;
  }
}
