/*
 * Card Styles
 * This is the single source of truth for all card styles
 */

/* Base card styles */
.post-card, .card-container {
  /* Structure */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0.5rem;

  /* Appearance */
  background-color: rgba(15, 22, 35, 0.7);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow: 0 4px 15px -3px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(54, 199, 255, 0.1);
  border: 1px solid rgba(26, 35, 51, 0.8);

  /* Animation */
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effect for non-expanded cards */
.post-card:hover:not(.expanded-post),
.card-container:hover:not(.expanded-card) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px -3px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(54, 199, 255, 0.2);
}

/* Card header */
.post-card-header, .card-header {
  padding: 1rem 1rem;
}

/* Card title spacing */
.post-card-header h3, .card-header h3 {
  padding-bottom: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Metadata divider */
.post-card-header .text-xs, .card-header .text-xs {
  border-bottom: 1px solid rgba(26, 35, 51, 0.2);
  padding-bottom: 0.25rem;
  margin-bottom: 0;
}

/* Card body */
.post-card-body, .card-body {
  padding: 0 1rem 1rem 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Card footer */
.post-card-footer, .card-footer {
  padding: 0.75rem 1rem;
  margin-top: auto;
  background-color: rgba(26, 35, 51, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-top: 1px solid rgba(26, 35, 51, 0.8);
}

/* Make sure the username doesn't overflow on small screens */
.post-card-footer .truncate {
  max-width: 140px;
}

/* Style the Read More button */
.post-card-footer button {
  padding: 0.25rem 0;
  margin-top: 0.25rem;
}

/* Expanded post styles */
.expanded-post {
  position: relative;
  background-color: rgba(15, 22, 35, 0.85);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(54, 199, 255, 0.2);
  border-color: rgba(54, 199, 255, 0.2);
  z-index: 10;
  scroll-margin: calc(50vh - 200px);

  /* Full-width expansion styles */
  box-sizing: border-box !important;
  padding-bottom: 1rem !important;

  /* Note: grid-column and grid-row are set dynamically by JavaScript */
  /* This ensures the post stays in its original row while spanning the full width */
  width: 100% !important;
  justify-self: stretch !important;
}

/* Expanded post header */
.expanded-post .post-card-header {
  padding: 1.25rem 1.25rem 0.75rem 1.25rem;
}

/* Expanded post title */
.expanded-post h3 {
  font-size: 1.5rem;
  line-height: 1.75;
  padding-bottom: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Expanded post body */
.expanded-post .post-card-body {
  padding: 0.5rem 1.25rem 1.25rem 1.25rem;
  max-height: 500px; /* Increased max height */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Expanded post content */
.expanded-post p {
  max-height: none;
  overflow: visible;
  font-size: 1rem;
  line-height: 1.5;
  word-wrap: break-word; /* Ensure long words don't overflow */
  overflow-wrap: break-word; /* Modern browsers */
  hyphens: auto; /* Add hyphens for very long words */
}

/* Expanded post footer */
.expanded-post .post-card-footer {
  padding: 0.75rem 1rem;
  background-color: rgba(26, 35, 51, 0.7);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  border-top: 1px solid rgba(54, 199, 255, 0.1);
}

/* Expanded post actions */
.expanded-post .flex.flex-wrap.mt-3 {
  margin-top: 1.5rem;
}

/* Custom scrollbar for expanded post content */
.expanded-post .post-card-body::-webkit-scrollbar {
  width: 6px;
}

.expanded-post .post-card-body::-webkit-scrollbar-track {
  background: rgba(26, 35, 51, 0.3);
  border-radius: 10px;
}

.expanded-post .post-card-body::-webkit-scrollbar-thumb {
  background: rgba(14, 144, 255, 0.5);
  border-radius: 10px;
}

.expanded-post .post-card-body::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 144, 255, 0.7);
}

/* Animation for posts moving to make room */
.posts-grid.has-expanded-post .post-card:not(.expanded-post) {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              grid-row 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              margin 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* Add extra margin to cards after expanded post */
  margin-top: 1rem;

  /* Ensure posts don't try to fill the same row as the expanded post */
  grid-column: auto !important;

  /* The grid-row property will be set dynamically by JavaScript */
  /* to ensure posts in the same row are pushed below */
}

/* Full-width row expansion styles */
.posts-grid.has-expanded-post .expanded-post {
  /* Full width styles */
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;

  /* Add some padding for better content display */
  padding: 1.5rem !important;

  /* Ensure proper z-index for overlapping */
  position: relative;
  z-index: 10;

  /* Animation for smooth transition */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  /* Ensure the post stands out */
  margin: 1.5rem 0 !important;
  box-shadow: 0 0 20px rgba(54, 199, 255, 0.5), 0 10px 25px rgba(0, 0, 0, 0.5) !important;

  /* Note: grid-column and grid-row are set dynamically by JavaScript */
  /* This ensures the post stays in its original row while spanning the full width */
}



/* Responsive styles */
/* Small screens (mobile) */
@media (max-width: 479px) {
  /* Ensure expanded posts have extra spacing on small screens */
  .expanded-post {
    margin: 2.5rem 0 !important; /* Increased margin for small screens */
    padding-bottom: 1.5rem !important;
  }

  /* Increase spacing in the grid when a post is expanded */
  .posts-grid.has-expanded-post {
    gap: 3rem !important; /* Larger gap for small screens */
  }

  /* Ensure the post body has a reasonable max height on small screens */
  .expanded-post .post-card-body {
    max-height: 400px; /* Slightly smaller on mobile */
  }
}

@media (min-width: 480px) {
  .post-card-footer {
    padding: 0.75rem;
  }

  .post-card-footer .truncate {
    max-width: 180px;
  }

  .post-card-footer .grid {
    grid-template-columns: auto auto;
    align-items: center;
    justify-content: space-between;
  }

  .post-card-footer button {
    width: auto;
    padding: 0.25rem 0.5rem;
    margin-top: 0;
  }

  .post-card-footer .text-center {
    text-align: right;
  }

  /* Medium-sized screens */
  .expanded-post {
    margin: 2.25rem 0 !important;
  }

  .posts-grid.has-expanded-post {
    gap: 2.75rem !important;
  }
}

@media (min-width: 640px) {
  .post-card-footer {
    padding: 0.75rem 1rem;
  }

  .expanded-post .post-card-footer {
    padding: 1rem 1.25rem;
  }

  .post-card-footer .truncate {
    max-width: 200px;
  }

  .post-card-footer .icon {
    height: 1.25rem;
    width: 1.25rem;
    margin-right: 0.375rem;
  }

  .post-card-footer span,
  .post-card-footer button {
    font-size: 0.875rem;
  }
}
