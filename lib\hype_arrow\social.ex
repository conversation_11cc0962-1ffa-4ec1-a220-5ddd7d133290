defmodule HypeArrow.Social do
  @moduledoc """
  The Social context.
  This context handles all social media related functionality.
  """

  import Ecto.Query, warn: false
  alias HypeArrow.Repo
  alias HypeArrow.Social.Post
  alias HypeArrow.Social.Authorization
  alias HypeArrow.Pagination
  alias HypeArrow.RealTime

  @default_posts_per_page 10

  @doc """
  Returns the list of posts.

  ## Examples

      iex> list_posts()
      [%Post{}, ...]

  """
  def list_posts do
    Post
    |> order_by([p], desc: p.published_at)
    |> Repo.all()
    |> Repo.preload(:user)
  end

  @doc """
  Returns a paginated list of posts.

  ## Parameters

  * `cursor` - The cursor value to start from (default: nil)
  * `limit` - The maximum number of posts to return (default: 10)
  * `direction` - The direction to paginate (:after or :before, default: :after)

  ## Examples

      iex> paginate_posts()
      {[%Post{}, ...], %{has_more: true, next_cursor: "2023-01-01T00:00:00Z"}}

      iex> paginate_posts("2023-01-01T00:00:00Z")
      {[%Post{}, ...], %{has_more: false, next_cursor: nil}}
  """
  def paginate_posts(cursor \\ nil, limit \\ @default_posts_per_page, direction \\ :after) do
    query = from(p in Post)

    # Convert string cursor to DateTime if provided
    cursor_value =
      if is_binary(cursor) and cursor != "",
        do: DateTime.from_iso8601(cursor) |> elem(1),
        else: cursor

    # Apply pagination
    {query, meta} =
      Pagination.paginate_query(query, :published_at, cursor_value, direction, limit)

    # Execute query
    results =
      query
      |> Repo.all()
      |> Repo.preload(:user)

    # Process results
    Pagination.process_results(results, meta, :published_at)
  end

  @doc """
  Gets a single post.

  Raises `Ecto.NoResultsError` if the Post does not exist.

  ## Examples

      iex> get_post!(123)
      %Post{}

      iex> get_post!(456)
      ** (Ecto.NoResultsError)

  """
  def get_post!(id), do: Repo.get!(Post, id)

  @doc """
  Gets a single post.

  Returns nil if the Post does not exist.

  ## Examples

      iex> get_post(123)
      %Post{}

      iex> get_post(456)
      nil

  """
  def get_post(id) do
    Post
    |> Repo.get(id)
    |> Repo.preload(:user)
  end

  @doc """
  Creates a post.

  ## Examples

      iex> create_post(%{field: value})
      {:ok, %Post{}}

      iex> create_post(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_post(attrs \\ %{}, user \\ nil) do
    # Add user_id to attrs if user is provided
    attrs = if user, do: Map.put(attrs, "user_id", user.id), else: attrs

    result =
      %Post{}
      |> Post.changeset(attrs)
      |> Repo.insert()

    case result do
      {:ok, post} ->
        # Preload the user for the post
        post = Repo.preload(post, :user)
        # Broadcast to all subscribers that a new post was created
        RealTime.broadcast(:posts, :post_created, %{post: post})
        {:ok, post}

      _ ->
        result
    end
  end

  @doc """
  Updates a post.

  ## Examples

      iex> update_post(post, %{field: new_value})
      {:ok, %Post{}}

      iex> update_post(post, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_post(%Post{} = post, attrs, current_user \\ nil) do
    # Check authorization if current_user is provided
    with {:ok, _} <- authorize_post_action(post, current_user, :update) do
      result =
        post
        |> Post.changeset(attrs)
        |> Repo.update()

      case result do
        {:ok, updated_post} ->
          # Preload the user for the post
          updated_post = Repo.preload(updated_post, :user)
          # Broadcast to all subscribers that a post was updated
          RealTime.broadcast(:posts, :post_updated, %{post: updated_post})
          # Also broadcast to subscribers of this specific post
          RealTime.broadcast(:post, updated_post.id, :post_updated, %{post: updated_post})
          {:ok, updated_post}

        _ ->
          result
      end
    end
  end

  @doc """
  Deletes a post.

  ## Examples

      iex> delete_post(post)
      {:ok, %Post{}}

      iex> delete_post(post)
      {:error, %Ecto.Changeset{}}

  """
  def delete_post(%Post{} = post, current_user \\ nil) do
    # Check authorization if current_user is provided
    with {:ok, _} <- authorize_post_action(post, current_user, :delete) do
      result = Repo.delete(post)

      case result do
        {:ok, deleted_post} ->
          # Broadcast to all subscribers that a post was deleted
          RealTime.broadcast(:posts, :post_deleted, %{post: deleted_post})
          # Also broadcast to subscribers of this specific post
          RealTime.broadcast(:post, deleted_post.id, :post_deleted, %{post: deleted_post})
          result

        _ ->
          result
      end
    end
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking post changes.

  ## Examples

      iex> change_post(post)
      %Ecto.Changeset{data: %Post{}}

  """
  def change_post(%Post{} = post, attrs \\ %{}) do
    Post.changeset(post, attrs)
  end

  # Helper function to authorize post actions
  defp authorize_post_action(post, nil, _action), do: {:ok, post}

  defp authorize_post_action(post, current_user, action) do
    Authorization.authorize_post(current_user, post, action)
  end
end
