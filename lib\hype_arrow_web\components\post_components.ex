defmodule HypeArrowWeb.PostComponents do
  @moduledoc """
  Provides UI components for posts.

  These components handle the rendering of post cards and related elements.
  """
  use Phoenix.Component
  use HypeArrowWeb, :verified_routes

  # Import components
  import HypeArrowWeb.ContentGridComponents
  import HypeArrowWeb.Components.SocialComponents
  import HypeArrowWeb.Components.ButtonComponents
  import HypeArrowWeb.Components.CardComponents, only: [card_container: 1]
  import HypeArrowWeb.Components.AvatarComponents

  # Alias utility modules
  alias HypeArrowWeb.Utils.FormatUtils
  alias Phoenix.LiveView.JS

  @doc """
  Renders a grid of post cards.

  ## Examples

      <.post_grid id="posts-container" posts={@posts} expanded_post_id={@expanded_post_id} on_toggle={&toggle_post/2}>
      </.post_grid>
  """
  attr :id, :string, default: "posts-container"
  attr :posts, :list, required: true
  attr :expanded_post_id, :integer, default: nil
  attr :on_toggle, :any, required: true
  attr :class, :string, default: nil
  attr :grid_size, :integer, default: 3
  attr :current_user, :map, default: nil
  attr :editing_post_id, :integer, default: nil
  attr :edit_form, :any, default: nil

  def post_grid(assigns) do
    ~H"""
    <.content_grid
      id={@id}
      items={@posts}
      grid_size={@grid_size}
      expanded_item_id={@expanded_post_id}
      item_id_key={:id}
      class={@class}
      phx_hook="PostsContainer"
    >
      <:item :let={{item, index, expanded}}>
        <.post_card
          post={item}
          index={index}
          expanded={expanded}
          on_toggle={@on_toggle}
          current_user={@current_user}
          editing_post_id={@editing_post_id}
          edit_form={@edit_form}
        />
      </:item>
    </.content_grid>
    """
  end

  @doc """
  Renders a post card.

  ## Examples

      <.post_card post={post} index={0} expanded={false} on_toggle={&toggle_post/2} />
  """
  attr :post, :map, required: true
  attr :index, :integer, required: true
  attr :expanded, :boolean, default: false
  attr :on_toggle, :any, required: true
  attr :class, :string, default: nil
  attr :current_user, :map, default: nil
  attr :editing_post_id, :integer, default: nil
  attr :edit_form, :any, default: nil

  def post_card(assigns) do
    ~H"""
    <.card_container
      id={"post-#{@post.id}"}
      index={@index}
      expanded={@expanded}
      on_click={if !@expanded && !(@editing_post_id == @post.id), do: @on_toggle.(@post.id)}
      class={"post-card #{if @expanded, do: "expanded-post", else: ""} #{@class}"}
    >
      <:header>
        <.post_header
          post={@post}
          expanded={@expanded}
          on_toggle={@on_toggle}
          current_user={@current_user}
          editing={@editing_post_id == @post.id}
        />
      </:header>
      <:body>
        <.post_body
          post={@post}
          expanded={@expanded}
          on_toggle={@on_toggle}
          editing={@editing_post_id == @post.id}
          form={@edit_form}
        />
      </:body>
      <:footer>
        <.post_footer post={@post} expanded={@expanded} on_toggle={@on_toggle} />
      </:footer>
    </.card_container>
    """
  end

  @doc """
  Renders a post card header.

  ## Examples

      <.post_header post={post} expanded={false} on_toggle={&toggle_post/2} />
  """
  attr :post, :map, required: true
  attr :expanded, :boolean, default: false
  attr :on_toggle, :any, required: true
  attr :class, :string, default: nil
  attr :current_user, :map, default: nil
  attr :editing, :boolean, default: false

  def post_header(assigns) do
    ~H"""
    <div class={[
      "post-card-header",
      @class
    ]}>
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold text-white">{@post.title}</h3>
        <div class="flex items-center space-x-1">
          <%= if @current_user && @post.user && @current_user.id == @post.user.id && !@editing do %>
            <.icon_only_button
              icon="hero-pencil"
              variant="ghost"
              size="sm"
              phx-click="toggle-edit-post"
              phx-value-id={@post.id}
              title="Edit post"
            />
            <.icon_only_button
              icon="hero-trash"
              variant="ghost"
              size="sm"
              phx-click="delete-post"
              phx-value-id={@post.id}
              data-confirm="Are you sure you want to delete this post?"
              title="Delete post"
            />
          <% end %>
          <%= if @expanded do %>
            <.icon_only_button
              icon="hero-x-mark"
              variant="ghost"
              size="sm"
              phx-click={@on_toggle.(@post.id)}
              title="Close post"
            />
          <% end %>
        </div>
      </div>
      <div class="flex items-center text-xs text-gray-400">
        <%= if @expanded do %>
          <.avatar_with_name
            name={if @post.user, do: @post.user.username, else: "Anonymous User"}
            size="sm"
            subtitle={FormatUtils.format_standard_date(@post.published_at)}
            class="mt-2"
          />
        <% else %>
          <span>Posted {FormatUtils.format_date(@post.published_at)}</span>
        <% end %>
      </div>
    </div>
    """
  end

  @doc """
  Renders a post card body.

  ## Examples

      <.post_body post={post} expanded={false} on_toggle={&toggle_post/2} />
  """
  attr :post, :map, required: true
  attr :expanded, :boolean, default: false
  attr :on_toggle, :any, required: true
  attr :class, :string, default: nil
  attr :editing, :boolean, default: false
  attr :form, :any, default: nil

  def post_body(assigns) do
    ~H"""
    <div class={
      [
        "post-card-body",
        # Increased height to accommodate social buttons
        !@expanded && "max-h-40",
        @expanded && "py-4",
        @class
      ]
    }>
      <%= if @editing && @expanded do %>
        <div class="space-y-4">
          <.form
            :let={_f}
            for={@form}
            phx-submit="save-inline-edit"
            id="edit-post-form"
            class="space-y-4"
          >
            <div class="mb-4">
              <label class="block text-gray-300 text-sm font-medium mb-2">Title</label>
              <input
                type="text"
                name="post[title]"
                value={@post.title}
                class="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-brand"
                required
              />
            </div>
            <div class="mb-4">
              <label class="block text-gray-300 text-sm font-medium mb-2">Content</label>
              <textarea
                name="post[content]"
                class="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-brand"
                rows="6"
                required
              ><%= @post.content %></textarea>
            </div>
            <input type="hidden" name="post_id" value={@post.id} />
          </.form>

          <div class="flex justify-end mt-4">
            <.icon_button
              icon="hero-x-mark"
              variant="ghost"
              phx-click="cancel-edit-post"
              class="mr-3 text-gray-400 hover:text-white"
            >
              Cancel
            </.icon_button>
            <.icon_button
              icon="hero-check"
              variant="primary"
              phx-click={JS.dispatch("submit", to: "#edit-post-form")}
              class="text-white"
            >
              Save
            </.icon_button>
          </div>
        </div>
      <% else %>
        <p class={[
          "text-gray-300 text-sm mt-0",
          !@expanded && "line-clamp-3",
          @expanded && "text-base leading-relaxed"
        ]}>
          <%= if @expanded do %>
            {@post.content}
          <% else %>
            {FormatUtils.truncate_content(@post.content, 150)}
          <% end %>
        </p>
        <div class={[
          "mt-4",
          @expanded && "border-t border-gray-700 pt-4"
        ]}>
          <div class="flex justify-between items-center">
            <.social_action_buttons likes_count={@post.likes_count} />
            <%= if @expanded do %>
              <.icon_button
                icon="hero-arrow-left"
                variant="ghost"
                phx-click={@on_toggle.(@post.id)}
                class="text-brand text-xs"
              >
                Close
              </.icon_button>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  @doc """
  Renders a post card footer.

  ## Examples

      <.post_footer post={post} expanded={false} on_toggle={&toggle_post/2} />
  """
  attr :post, :map, required: true
  attr :expanded, :boolean, default: false
  attr :on_toggle, :any, required: true
  attr :class, :string, default: nil

  def post_footer(assigns) do
    ~H"""
    <div class={[
      "post-card-footer",
      @expanded && "hidden",
      @class
    ]}>
      <div class="grid grid-cols-1 w-full gap-1">
        <div class="flex items-center">
          <.avatar_with_name
            name={if @post.user, do: @post.user.username, else: "Anonymous User"}
            size="sm"
            subtitle={FormatUtils.format_standard_date(@post.published_at)}
          />
        </div>
        <div class="text-center mt-2">
          <.icon_button
            icon="hero-arrow-right"
            variant="ghost"
            phx-click={@on_toggle.(@post.id)}
            class="text-brand text-xs w-full"
          >
            Read More
          </.icon_button>
        </div>
      </div>
    </div>
    """
  end
end
