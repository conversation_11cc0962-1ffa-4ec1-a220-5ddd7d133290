defmodule HypeArrowWeb.PostLive.PostUtils do
  @moduledoc """
  Utility functions for working with posts in LiveView.
  """

  @doc """
  Deduplicate posts by ID.
  """
  def deduplicate_posts(posts) do
    posts
    |> Enum.uniq_by(& &1.id)
  end

  @doc """
  Update a post in a list of posts.
  """
  def update_post_in_list(posts, updated_post) do
    Enum.map(posts, fn post ->
      if post.id == updated_post.id do
        updated_post
      else
        post
      end
    end)
  end

  @doc """
  Remove a post from a list of posts.
  """
  def remove_post_from_list(posts, post_id) do
    Enum.reject(posts, fn post -> post.id == post_id end)
  end

  @doc """
  Merge two lists of posts, avoiding duplicates.
  """
  def merge_posts(existing_posts, new_posts) do
    existing_ids = MapSet.new(existing_posts, & &1.id)

    # Filter out new posts that already exist in the existing posts
    unique_new_posts =
      Enum.reject(new_posts, fn post -> MapSet.member?(existing_ids, post.id) end)

    # Combine the lists
    existing_posts ++ unique_new_posts
  end

  @doc """
  Ensure a post is at the top of the list.
  """
  def ensure_post_at_top(posts, post_id) do
    case Enum.split_with(posts, fn post -> post.id == post_id end) do
      {[post], other_posts} -> [post | other_posts]
      {[], _} -> posts
    end
  end
end
