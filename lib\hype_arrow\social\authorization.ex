defmodule HypeArrow.Social.Authorization do
  @moduledoc """
  Authorization helpers for the Social context.
  """

  alias Hype<PERSON>rrow.Social.Post
  alias HypeArrow.Accounts.User

  @doc """
  Checks if a user can perform an action on a post.

  ## Examples

      iex> can_manage_post?(user, post)
      true

      iex> can_manage_post?(different_user, post)
      false

  """
  def can_manage_post?(%User{} = user, %Post{} = post) do
    # User can manage their own posts
    post.user_id == user.id
  end

  def can_manage_post?(_, _), do: false

  @doc """
  Authorizes a post action.

  ## Examples

      iex> authorize_post(user, post, :update)
      {:ok, post}

      iex> authorize_post(different_user, post, :update)
      {:error, :unauthorized}

  """
  def authorize_post(%User{} = user, %Post{} = post, _action) do
    if can_manage_post?(user, post) do
      {:ok, post}
    else
      {:error, :unauthorized}
    end
  end

  def authorize_post(_, _, _), do: {:error, :unauthorized}
end
