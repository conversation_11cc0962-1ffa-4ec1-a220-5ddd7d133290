defmodule HypeArrowWeb.Theme do
  @moduledoc """
  Provides theme configuration for the application.
  
  This module centralizes theme-related constants and functions to ensure
  consistent styling across the application.
  """
  
  @doc """
  Returns the color palette for the application.
  """
  def colors do
    %{
      brand: %{
        default: "#0E90FF",
        dark: "#0A1A2F",
        light: "#36C7FF",
        purple: "#8A2BE2"
      },
      background: %{
        dark: "#0A0E17",
        default: "#0F1623",
        light: "#1A2333"
      },
      text: %{
        primary: "#FFFFFF",
        secondary: "#A0AEC0",
        muted: "#718096"
      },
      border: %{
        default: "rgba(26, 35, 51, 0.8)",
        light: "rgba(54, 199, 255, 0.1)"
      },
      card: %{
        background: "rgba(15, 22, 35, 0.7)",
        border: "rgba(26, 35, 51, 0.8)",
        highlight: "rgba(54, 199, 255, 0.1)"
      }
    }
  end
  
  @doc """
  Returns the spacing scale for the application.
  """
  def spacing do
    %{
      xs: "0.25rem",
      sm: "0.5rem",
      md: "1rem",
      lg: "1.5rem",
      xl: "2rem",
      "2xl": "3rem",
      "3xl": "4rem"
    }
  end
  
  @doc """
  Returns the border radius scale for the application.
  """
  def border_radius do
    %{
      sm: "0.25rem",
      md: "0.375rem",
      lg: "0.5rem",
      xl: "0.75rem",
      full: "9999px"
    }
  end
  
  @doc """
  Returns the font size scale for the application.
  """
  def font_sizes do
    %{
      xs: "0.75rem",
      sm: "0.875rem",
      md: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem"
    }
  end
  
  @doc """
  Returns the font weight scale for the application.
  """
  def font_weights do
    %{
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700"
    }
  end
  
  @doc """
  Returns the line height scale for the application.
  """
  def line_heights do
    %{
      none: "1",
      tight: "1.25",
      normal: "1.5",
      relaxed: "1.75",
      loose: "2"
    }
  end
  
  @doc """
  Returns the shadow scale for the application.
  """
  def shadows do
    %{
      sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
      md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      xl: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      card: "0 4px 15px -3px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(54, 199, 255, 0.1)"
    }
  end
  
  @doc """
  Returns the z-index scale for the application.
  """
  def z_indices do
    %{
      base: "0",
      above: "10",
      below: "-10",
      dropdown: "1000",
      sticky: "1100",
      fixed: "1200",
      modal: "1300",
      popover: "1400",
      tooltip: "1500"
    }
  end
  
  @doc """
  Returns the transition properties for the application.
  """
  def transitions do
    %{
      default: "all 0.3s ease",
      fast: "all 0.15s ease",
      slow: "all 0.5s ease"
    }
  end
  
  @doc """
  Returns the backdrop filter values for the application.
  """
  def backdrop_filters do
    %{
      card: "blur(4px)",
      modal: "blur(8px)"
    }
  end
  
  @doc """
  Returns the gradient definitions for the application.
  """
  def gradients do
    %{
      brand: "linear-gradient(135deg, #36C7FF 0%, #0E90FF 50%, #8A2BE2 100%)",
      background: "linear-gradient(135deg, #0A0E17 0%, #0F1623 50%, #1A2333 100%)"
    }
  end
end
