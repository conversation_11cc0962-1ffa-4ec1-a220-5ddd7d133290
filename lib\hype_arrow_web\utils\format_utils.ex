defmodule HypeArrowWeb.Utils.FormatUtils do
  @moduledoc """
  Utility functions for formatting data in UI components.

  This module provides common utility functions that can be used across
  different components in the application.
  """

  @doc """
  Formats a datetime into a human-readable string.

  ## Examples

      iex> format_date(~U[2023-01-01 00:00:00Z])
      "1 year ago"
  """
  def format_date(nil), do: "Unknown date"

  def format_date(datetime) do
    now = DateTime.utc_now()
    diff = DateTime.diff(now, datetime, :second)

    cond do
      diff < 60 ->
        "just now"

      diff < 3600 ->
        "#{div(diff, 60)} #{pluralize(div(diff, 60), "minute")} ago"

      diff < 86400 ->
        "#{div(diff, 3600)} #{pluralize(div(diff, 3600), "hour")} ago"

      diff < 172_800 ->
        "yesterday"

      diff < 2_592_000 ->
        "#{div(diff, 86400)} #{pluralize(div(diff, 86400), "day")} ago"

      diff < 31_536_000 ->
        "#{div(diff, 2_592_000)} #{pluralize(div(diff, 2_592_000), "month")} ago"

      true ->
        "#{div(diff, 31_536_000)} #{pluralize(div(diff, 31_536_000), "year")} ago"
    end
  end

  @doc """
  Truncates content to a specified maximum length and adds ellipsis if needed.

  ## Examples

      iex> truncate_content("This is a long text", 10)
      "This is a ..."
  """
  def truncate_content(nil, _), do: ""

  def truncate_content(content, max_length) do
    if String.length(content) > max_length do
      String.slice(content, 0, max_length) <> "..."
    else
      content
    end
  end

  @doc """
  Formats a number with commas for thousands.

  ## Examples

      iex> format_number(1234)
      "1,234"
  """
  def format_number(nil), do: "0"

  def format_number(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  @doc """
  Formats a date in a standard format (Month Day, Year).

  ## Examples

      iex> format_standard_date(~U[2023-01-01 00:00:00Z])
      "Jan 1, 2023"
  """
  def format_standard_date(nil), do: "Unknown date"

  def format_standard_date(date) do
    Calendar.strftime(date, "%b %d, %Y")
  end

  # Helper function to pluralize words
  defp pluralize(1, word), do: word
  defp pluralize(_, word), do: word <> "s"
end
