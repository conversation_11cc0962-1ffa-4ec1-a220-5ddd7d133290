# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     HypeArrow.Repo.insert!(%HypeArrow.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

alias HypeArrow.Repo
alias HypeArrow.Social.Post
alias HypeArrow.Accounts
alias HypeArrow.Accounts.User

# Delete existing users and posts
Repo.delete_all(Post)
Repo.delete_all(User)

# Create users
IO.puts("Seeding users...")

users = [
  %{
    email: "<EMAIL>",
    password: "Password123!",
    username: "admin"
  },
  %{
    email: "<EMAIL>",
    password: "Password123!",
    username: "user1"
  },
  %{
    email: "<EMAIL>",
    password: "Password123!",
    username: "user2"
  }
]

created_users =
  Enum.map(users, fn user_attrs ->
    {:ok, user} = Accounts.register_user(user_attrs)
    user
  end)

admin_user = List.first(created_users)
user1 = Enum.at(created_users, 1)
user2 = Enum.at(created_users, 2)

IO.puts("Successfully seeded #{length(created_users)} users!")

# Then seed with our posts
IO.puts("Seeding posts...")

# Create sample posts with different published dates
now = DateTime.utc_now() |> DateTime.truncate(:second)
yesterday = DateTime.add(now, -1, :day) |> DateTime.truncate(:second)
last_week = DateTime.add(now, -7, :day) |> DateTime.truncate(:second)

posts = [
  %{
    title: "Welcome to HypeArrow!",
    content:
      "This is the first post on our exciting new social media platform. We're thrilled to have you here!",
    likes_count: 42,
    published_at: last_week
  },
  %{
    title: "How to get the most out of HypeArrow",
    content:
      "Here are some tips to enhance your experience: 1) Follow topics you're interested in, 2) Engage with other users' content, 3) Share your own thoughts regularly.",
    likes_count: 27,
    published_at: yesterday
  },
  %{
    title: "Upcoming Features",
    content:
      "We're working on some exciting new features including enhanced notifications, direct messaging, and custom themes. Stay tuned!",
    likes_count: 15,
    published_at: now
  },
  %{
    title: "Community Guidelines",
    content:
      "Please remember to be respectful to other users. We want HypeArrow to be a positive and inclusive space for everyone.",
    likes_count: 31,
    published_at: yesterday
  },
  %{
    title: "Tech Stack Behind HypeArrow",
    content:
      "HypeArrow is built with Elixir and Phoenix, providing incredible performance and real-time capabilities. The frontend uses Phoenix LiveView for reactive UI without complex JavaScript frameworks.",
    likes_count: 23,
    published_at: now
  },
  %{
    title: "The Future of Social Media",
    content:
      "Social media is evolving rapidly. At HypeArrow, we're committed to building a platform that prioritizes user experience, privacy, and meaningful connections over addictive algorithms.",
    likes_count: 56,
    published_at: DateTime.add(now, -2, :day)
  },
  %{
    title: "Meet the Team",
    content:
      "Our diverse team of developers, designers, and community managers are working hard to make HypeArrow the best social platform. We come from various backgrounds but share a passion for creating meaningful online experiences.",
    likes_count: 38,
    published_at: DateTime.add(now, -3, :day)
  },
  %{
    title: "Privacy First Approach",
    content:
      "Unlike other platforms, HypeArrow is designed with privacy as a core principle. We don't track your behavior, sell your data, or use manipulative algorithms to keep you engaged.",
    likes_count: 72,
    published_at: DateTime.add(now, -4, :day)
  },
  %{
    title: "Content Moderation Philosophy",
    content:
      "We believe in community-driven moderation with clear guidelines. Our approach balances free expression with the need to maintain a respectful environment for all users.",
    likes_count: 29,
    published_at: DateTime.add(now, -5, :day)
  },
  %{
    title: "Elixir: The Secret to Our Performance",
    content:
      "Elixir and the BEAM VM allow us to handle millions of concurrent connections with minimal resources. This technology stack is what makes our real-time features so responsive and reliable.",
    likes_count: 45,
    published_at: DateTime.add(now, -6, :day)
  },
  %{
    title: "Phoenix LiveView: Reactive UIs Without the Complexity",
    content:
      "We've built HypeArrow using Phoenix LiveView, which gives us the reactivity of modern JavaScript frameworks with the simplicity and reliability of server-rendered HTML. This means a faster, more accessible experience for everyone.",
    likes_count: 33,
    published_at: DateTime.add(now, -8, :day)
  },
  %{
    title: "Accessibility Matters",
    content:
      "We're committed to making HypeArrow accessible to everyone. Our platform follows WCAG guidelines and we continuously work to improve the experience for users with disabilities.",
    likes_count: 41,
    published_at: DateTime.add(now, -9, :day)
  },
  %{
    title: "Open Source Contributions",
    content:
      "HypeArrow is built on open source technology, and we're proud to give back to the community. We've contributed to several Elixir and Phoenix projects and plan to open source some of our own components in the future.",
    likes_count: 37,
    published_at: DateTime.add(now, -10, :day)
  },
  %{
    title: "Sustainable Growth Strategy",
    content:
      "We're focused on sustainable, organic growth rather than the 'growth at all costs' mentality. This means we prioritize user satisfaction and retention over raw user acquisition numbers.",
    likes_count: 28,
    published_at: DateTime.add(now, -11, :day)
  },
  %{
    title: "The Importance of Community",
    content:
      "Social media should bring people together. We're designing HypeArrow to foster meaningful communities around shared interests, values, and goals.",
    likes_count: 52,
    published_at: DateTime.add(now, -12, :day)
  },
  %{
    title: "Transparent Business Model",
    content:
      "We believe users should understand how a platform makes money. Our business model is simple and transparent: we offer premium features for power users while keeping the core platform free and ad-free.",
    likes_count: 39,
    published_at: DateTime.add(now, -13, :day)
  },
  %{
    title: "Mobile Apps Coming Soon",
    content:
      "We're working on native mobile apps for iOS and Android to complement our web experience. These apps will offer the same great features with optimized performance for mobile devices.",
    likes_count: 64,
    published_at: DateTime.add(now, -14, :day)
  },
  %{
    title: "Developer API Access",
    content:
      "We believe in an open ecosystem. Soon, developers will be able to build integrations and extensions for HypeArrow through our comprehensive API.",
    likes_count: 31,
    published_at: DateTime.add(now, -15, :day)
  },
  %{
    title: "International Expansion Plans",
    content:
      "HypeArrow is going global! We're working on translations and localization to make our platform accessible to users around the world.",
    likes_count: 47,
    published_at: DateTime.add(now, -16, :day)
  },
  %{
    title: "Join Our Beta Testing Program",
    content:
      "Want early access to new features? Join our beta testing program to help shape the future of HypeArrow and provide valuable feedback on upcoming features.",
    likes_count: 58,
    published_at: DateTime.add(now, -17, :day)
  }
]

# Insert all posts with users
posts_with_users =
  Enum.with_index(posts, fn post_attrs, index ->
    # Assign posts to different users in a round-robin fashion
    user = Enum.at(created_users, rem(index, length(created_users)))
    Map.put(post_attrs, :user_id, user.id)
  end)

Enum.each(posts_with_users, fn post_attrs ->
  %Post{}
  |> Post.changeset(post_attrs)
  |> Repo.insert!()
end)

IO.puts("Successfully seeded #{length(posts)} posts!")
