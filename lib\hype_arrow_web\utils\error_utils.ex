defmodule HypeArrowWeb.Utils.ErrorUtils do
  @moduledoc """
  Utility functions for error handling.

  This module provides helper functions for handling errors consistently
  across the application.
  """

  # Import controller functions for handling errors in controllers
  import Phoenix.Controller, only: [redirect: 2]

  # Import LiveView functions for handling errors in LiveViews
  import Phoenix.LiveView, only: [push_navigate: 2, push_patch: 2]

  # Alias modules to use their functions directly
  alias Phoenix.Controller, as: Controller
  alias Phoenix.LiveView, as: LiveView

  @doc """
  Handles common errors in controllers.

  ## Examples

      def show(conn, %{"id" => id}) do
        case get_resource(id) do
          {:ok, resource} ->
            render(conn, "show.html", resource: resource)
          {:error, :not_found} ->
            ErrorUtils.handle_error(conn, :not_found, "Resource not found")
        end
      end
  """
  def handle_error(conn, error_type, message \\ nil)

  def handle_error(conn, :not_found, message) do
    message = message || "Resource not found"

    conn
    |> Controller.put_flash(:error, message)
    |> redirect(to: "/")
  end

  def handle_error(conn, :unauthorized, message) do
    message = message || "Unauthorized access"

    conn
    |> Controller.put_flash(:error, message)
    |> redirect(to: "/")
  end

  def handle_error(conn, :bad_request, message) do
    message = message || "Invalid request"

    conn
    |> Controller.put_flash(:error, message)
    |> redirect(to: "/")
  end

  @doc """
  Handles common errors in LiveViews.

  ## Examples

      def mount(%{"id" => id}, _session, socket) do
        case get_resource(id) do
          {:ok, resource} ->
            {:ok, assign(socket, :resource, resource)}
          {:error, :not_found} ->
            {:ok, ErrorUtils.handle_live_error(socket, :not_found, "Resource not found")}
        end
      end
  """
  def handle_live_error(socket, error_type, message \\ nil, opts \\ [])

  def handle_live_error(socket, :not_found, message, opts) do
    message = message || "Resource not found"
    navigate_to = Keyword.get(opts, :navigate_to, "/")
    use_patch = Keyword.get(opts, :use_patch, false)

    socket = LiveView.put_flash(socket, :error, message)

    if use_patch do
      push_patch(socket, to: navigate_to)
    else
      push_navigate(socket, to: navigate_to)
    end
  end

  def handle_live_error(socket, :unauthorized, message, opts) do
    message = message || "Unauthorized access"
    navigate_to = Keyword.get(opts, :navigate_to, "/")
    use_patch = Keyword.get(opts, :use_patch, false)

    socket = LiveView.put_flash(socket, :error, message)

    if use_patch do
      push_patch(socket, to: navigate_to)
    else
      push_navigate(socket, to: navigate_to)
    end
  end

  def handle_live_error(socket, :bad_request, message, opts) do
    message = message || "Invalid request"
    navigate_to = Keyword.get(opts, :navigate_to, "/")
    use_patch = Keyword.get(opts, :use_patch, false)

    socket = LiveView.put_flash(socket, :error, message)

    if use_patch do
      push_patch(socket, to: navigate_to)
    else
      push_navigate(socket, to: navigate_to)
    end
  end

  @doc """
  Formats changeset errors for display.

  ## Examples

      def create(conn, %{"user" => user_params}) do
        case create_user(user_params) do
          {:ok, user} ->
            redirect(conn, to: Routes.user_path(conn, :show, user))
          {:error, changeset} ->
            error_messages = ErrorUtils.format_changeset_errors(changeset)
            render(conn, "new.html", changeset: changeset, error_messages: error_messages)
        end
      end
  """
  def format_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
    |> Enum.map(fn {key, errors} ->
      "#{Phoenix.Naming.humanize(key)} #{Enum.join(errors, ", ")}"
    end)
  end

  @doc """
  Logs errors with appropriate severity.

  ## Examples

      def process_data(data) do
        try do
          # Process data
        rescue
          e ->
            ErrorUtils.log_error(:error, "Failed to process data", e)
            {:error, "Processing failed"}
        end
      end
  """
  def log_error(level, message, error \\ nil) do
    error_info = if error, do: " - #{inspect(error)}", else: ""
    full_message = "#{message}#{error_info}"

    case level do
      :debug -> IO.puts("DEBUG: #{full_message}")
      :info -> IO.puts("INFO: #{full_message}")
      :warn -> IO.puts("WARNING: #{full_message}")
      :error -> IO.puts("ERROR: #{full_message}")
      :critical -> IO.puts("CRITICAL: #{full_message}")
      _ -> IO.puts("LOG: #{full_message}")
    end
  end
end
