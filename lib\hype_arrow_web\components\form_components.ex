defmodule HypeArrowWeb.Components.FormComponents do
  @moduledoc """
  Provides form UI components for the application.

  These components handle rendering of form elements with consistent appearance.
  """
  use Phoenix.Component

  # Import components
  import HypeArrowWeb.CoreComponents

  @doc """
  Renders a form field with label and error messages.

  ## Examples

      <.form_field form={@form} field={:name} label="Name">
        <.input field={@form[:name]} type="text" />
      </.form_field>
  """
  attr :form, :any, required: true
  attr :field, :atom, required: true
  attr :label, :string, required: true
  attr :help_text, :string, default: nil
  attr :required, :boolean, default: false
  attr :class, :string, default: nil

  slot :inner_block, required: true

  def form_field(assigns) do
    ~H"""
    <div class={["mb-4", @class]}>
      <label for={@form[@field].id} class="block text-sm font-medium text-gray-200 mb-1">
        {@label}{if @required, do: " *"}
      </label>

      {render_slot(@inner_block)}

      <%= if @help_text do %>
        <p class="mt-1 text-xs text-gray-400">{@help_text}</p>
      <% end %>

      <.error :for={msg <- @form[@field].errors}>
        {if is_tuple(msg), do: elem(msg, 0), else: msg}
      </.error>
    </div>
    """
  end

  @doc """
  Renders a styled text input field.

  ## Examples

      <.styled_input field={@form[:name]} type="text" placeholder="Enter your name" />
  """
  attr :id, :any, default: nil
  attr :name, :any
  attr :label, :string, default: nil
  attr :value, :any
  attr :type, :string, default: "text"
  attr :placeholder, :string, default: nil
  attr :required, :boolean, default: false
  attr :disabled, :boolean, default: false
  attr :class, :string, default: nil

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, e.g. @form[:email]"

  attr :errors, :list, default: []

  attr :rest, :global,
    include: ~w(autocomplete autofocus max maxlength min minlength pattern readonly step)

  def styled_input(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(
      field: nil,
      id: field.id,
      name: field.name,
      value: field.value,
      errors: field.errors
    )
    |> styled_input()
  end

  def styled_input(assigns) do
    ~H"""
    <div class="relative">
      <input
        type={@type}
        name={@name}
        id={@id || @name}
        value={@value}
        placeholder={@placeholder}
        required={@required}
        disabled={@disabled}
        class={[
          "block w-full rounded-md border-0 bg-background-light/50 py-2 px-3",
          "text-white placeholder-gray-400 shadow-sm ring-1 ring-inset",
          "focus:ring-2 focus:ring-inset focus:ring-brand focus:outline-none",
          @errors != [] && "ring-red-500 focus:ring-red-500",
          @disabled && "opacity-50 cursor-not-allowed",
          @class
        ]}
        {@rest}
      />
      <%= if @label do %>
        <label
          for={@id || @name}
          class={[
            "absolute left-3 -top-2 px-1 text-xs font-medium",
            @errors != [] && "text-red-500",
            @errors == [] && "text-gray-400"
          ]}
        >
          {@label}{if @required, do: " *"}
        </label>
      <% end %>
    </div>
    """
  end

  @doc """
  Renders a styled textarea field.

  ## Examples

      <.styled_textarea field={@form[:content]} placeholder="Enter your content" rows={5} />
  """
  attr :id, :any, default: nil
  attr :name, :any
  attr :label, :string, default: nil
  attr :value, :any
  attr :placeholder, :string, default: nil
  attr :rows, :integer, default: 3
  attr :required, :boolean, default: false
  attr :disabled, :boolean, default: false
  attr :class, :string, default: nil

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, e.g. @form[:content]"

  attr :errors, :list, default: []
  attr :rest, :global, include: ~w(autocomplete autofocus maxlength minlength readonly)

  def styled_textarea(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(
      field: nil,
      id: field.id,
      name: field.name,
      value: field.value,
      errors: field.errors
    )
    |> styled_textarea()
  end

  def styled_textarea(assigns) do
    ~H"""
    <div class="relative">
      <textarea
        name={@name}
        id={@id || @name}
        placeholder={@placeholder}
        rows={@rows}
        required={@required}
        disabled={@disabled}
        class={[
          "block w-full rounded-md border-0 bg-background-light/50 py-2 px-3",
          "text-white placeholder-gray-400 shadow-sm ring-1 ring-inset",
          "focus:ring-2 focus:ring-inset focus:ring-brand focus:outline-none",
          @errors != [] && "ring-red-500 focus:ring-red-500",
          @disabled && "opacity-50 cursor-not-allowed",
          @class
        ]}
        {@rest}
      ><%= @value %></textarea>
      <%= if @label do %>
        <label
          for={@id || @name}
          class={[
            "absolute left-3 -top-2 px-1 text-xs font-medium",
            @errors != [] && "text-red-500",
            @errors == [] && "text-gray-400"
          ]}
        >
          {@label}{if @required, do: " *"}
        </label>
      <% end %>
    </div>
    """
  end

  @doc """
  Renders a styled select field.

  ## Examples

      <.styled_select field={@form[:category]} options={[{"Option 1", "1"}, {"Option 2", "2"}]} />
  """
  attr :id, :any, default: nil
  attr :name, :any
  attr :label, :string, default: nil
  attr :value, :any
  attr :options, :list, required: true
  attr :prompt, :string, default: nil
  attr :required, :boolean, default: false
  attr :disabled, :boolean, default: false
  attr :class, :string, default: nil

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, e.g. @form[:category]"

  attr :errors, :list, default: []
  attr :rest, :global

  def styled_select(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(
      field: nil,
      id: field.id,
      name: field.name,
      value: field.value,
      errors: field.errors
    )
    |> styled_select()
  end

  def styled_select(assigns) do
    ~H"""
    <div class="relative">
      <select
        name={@name}
        id={@id || @name}
        required={@required}
        disabled={@disabled}
        class={[
          "block w-full rounded-md border-0 bg-background-light/50 py-2 px-3",
          "text-white placeholder-gray-400 shadow-sm ring-1 ring-inset",
          "focus:ring-2 focus:ring-inset focus:ring-brand focus:outline-none",
          @errors != [] && "ring-red-500 focus:ring-red-500",
          @disabled && "opacity-50 cursor-not-allowed",
          @class
        ]}
        {@rest}
      >
        <%= if @prompt do %>
          <option value="">{@prompt}</option>
        <% end %>
        <%= for {label, value} <- @options do %>
          <option value={value} selected={to_string(@value) == to_string(value)}>
            {label}
          </option>
        <% end %>
      </select>
      <%= if @label do %>
        <label
          for={@id || @name}
          class={[
            "absolute left-3 -top-2 px-1 text-xs font-medium",
            @errors != [] && "text-red-500",
            @errors == [] && "text-gray-400"
          ]}
        >
          {@label}{if @required, do: " *"}
        </label>
      <% end %>
    </div>
    """
  end

  @doc """
  Renders a form actions section with submit and cancel buttons.

  ## Examples

      <.form_actions>
        <:cancel_button navigate={~p"/"}>Cancel</:cancel_button>
      </.form_actions>
  """
  attr :class, :string, default: nil
  attr :submit_label, :string, default: "Save"
  attr :submit_disabled, :boolean, default: false
  attr :submit_class, :string, default: nil

  slot :cancel_button do
    attr :navigate, :string
    attr :patch, :string
    attr :href, :string
    attr :class, :string
  end

  slot :extra_actions

  def form_actions(assigns) do
    ~H"""
    <div class={["flex items-center justify-end space-x-3 mt-6", @class]}>
      <%= for cancel <- @cancel_button do %>
        <.link
          navigate={cancel[:navigate]}
          patch={cancel[:patch]}
          href={cancel[:href]}
          class={[
            "hype-btn hype-btn-secondary",
            cancel[:class]
          ]}
        >
          {render_slot(cancel)}
        </.link>
      <% end %>

      <%= for extra <- @extra_actions do %>
        {render_slot(extra)}
      <% end %>

      <button
        type="submit"
        disabled={@submit_disabled}
        class={["hype-btn hype-btn-primary", @submit_class]}
      >
        {@submit_label}
      </button>
    </div>
    """
  end
end
