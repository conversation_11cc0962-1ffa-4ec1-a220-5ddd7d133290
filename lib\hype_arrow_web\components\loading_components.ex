defmodule HypeArrowWeb.Components.LoadingComponents do
  @moduledoc """
  Provides loading UI components for the application.
  
  These components handle rendering of various loading indicators with consistent appearance.
  """
  use Phoenix.Component
  
  @doc """
  Renders a spinner loading indicator.
  
  ## Examples
  
      <.spinner />
      <.spinner size="lg" color="brand" />
  """
  attr :size, :string, default: "md", values: ~w(sm md lg)
  attr :color, :string, default: "brand", values: ~w(brand white gray)
  attr :class, :string, default: nil
  
  def spinner(assigns) do
    ~H"""
    <div
      class={[
        "animate-spin rounded-full border-b-2",
        # Size classes
        @size == "sm" && "h-4 w-4",
        @size == "md" && "h-6 w-6",
        @size == "lg" && "h-8 w-8",
        # Color classes
        @color == "brand" && "border-brand",
        @color == "white" && "border-white",
        @color == "gray" && "border-gray-400",
        @class
      ]}
      aria-hidden="true"
    >
    </div>
    """
  end
  
  @doc """
  Renders a loading indicator with text.
  
  ## Examples
  
      <.loading_indicator>Loading posts...</.loading_indicator>
      <.loading_indicator size="lg" color="white">Processing...</.loading_indicator>
  """
  attr :size, :string, default: "md", values: ~w(sm md lg)
  attr :color, :string, default: "brand", values: ~w(brand white gray)
  attr :class, :string, default: nil
  slot :inner_block, required: true
  
  def loading_indicator(assigns) do
    ~H"""
    <div class={["flex items-center justify-center", @class]}>
      <.spinner size={@size} color={@color} />
      <span class={[
        "ml-2",
        # Size classes
        @size == "sm" && "text-xs",
        @size == "md" && "text-sm",
        @size == "lg" && "text-base",
        # Color classes
        @color == "brand" && "text-brand",
        @color == "white" && "text-white",
        @color == "gray" && "text-gray-400"
      ]}>
        {render_slot(@inner_block)}
      </span>
    </div>
    """
  end
  
  @doc """
  Renders a skeleton loading placeholder.
  
  ## Examples
  
      <.skeleton_placeholder height="h-4" width="w-full" />
      <.skeleton_placeholder height="h-32" width="w-64" rounded="rounded-lg" />
  """
  attr :height, :string, required: true
  attr :width, :string, required: true
  attr :rounded, :string, default: "rounded-md"
  attr :class, :string, default: nil
  
  def skeleton_placeholder(assigns) do
    ~H"""
    <div
      class={[
        "animate-pulse bg-gray-700 opacity-30",
        @height,
        @width,
        @rounded,
        @class
      ]}
      aria-hidden="true"
    >
    </div>
    """
  end
  
  @doc """
  Renders a post card skeleton loading placeholder.
  
  ## Examples
  
      <.post_card_skeleton />
      <.post_card_skeleton class="my-4" />
  """
  attr :class, :string, default: nil
  
  def post_card_skeleton(assigns) do
    ~H"""
    <div class={[
      "card-container",
      @class
    ]}>
      <div class="card-header">
        <div class="flex justify-between items-center">
          <.skeleton_placeholder height="h-6" width="w-3/4" />
        </div>
        <div class="mt-2">
          <.skeleton_placeholder height="h-4" width="w-1/3" />
        </div>
      </div>
      <div class="card-body">
        <.skeleton_placeholder height="h-4" width="w-full" class="mb-2" />
        <.skeleton_placeholder height="h-4" width="w-full" class="mb-2" />
        <.skeleton_placeholder height="h-4" width="w-2/3" class="mb-4" />
        
        <div class="flex flex-wrap mt-3 pt-3 border-t border-gray-800">
          <.skeleton_placeholder height="h-8" width="w-16" rounded="rounded-full" class="mr-4" />
          <.skeleton_placeholder height="h-8" width="w-24" rounded="rounded-full" class="mr-4" />
          <.skeleton_placeholder height="h-8" width="w-16" rounded="rounded-full" />
        </div>
      </div>
      <div class="card-footer">
        <div class="grid grid-cols-1 w-full gap-1">
          <div class="flex items-center">
            <.skeleton_placeholder height="h-4" width="w-4" rounded="rounded-full" class="mr-2" />
            <.skeleton_placeholder height="h-4" width="w-1/3" />
          </div>
          <div class="text-center mt-2">
            <.skeleton_placeholder height="h-8" width="w-full" rounded="rounded-md" />
          </div>
        </div>
      </div>
    </div>
    """
  end
  
  @doc """
  Renders a loading overlay that covers the entire container.
  
  ## Examples
  
      <.loading_overlay show={@loading}>
        <div>Content that will be covered by the overlay when loading</div>
      </.loading_overlay>
  """
  attr :show, :boolean, required: true
  attr :class, :string, default: nil
  attr :message, :string, default: "Loading..."
  slot :inner_block, required: true
  
  def loading_overlay(assigns) do
    ~H"""
    <div class="relative">
      {render_slot(@inner_block)}
      
      <%= if @show do %>
        <div class={[
          "absolute inset-0 flex items-center justify-center",
          "bg-background-dark bg-opacity-70 backdrop-blur-sm z-10",
          @class
        ]}>
          <.loading_indicator size="lg" color="brand">{@message}</.loading_indicator>
        </div>
      <% end %>
    </div>
    """
  end
end
